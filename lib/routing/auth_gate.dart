import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/auth/application/auth_service.dart';
import 'package:skillz/features/home/<USER>/home_page.dart'; // Placeholder for HomePage
import 'package:skillz/features/auth/presentation/login_page.dart';

class AuthGate extends ConsumerWidget {
  const AuthGate({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateChangesProvider);

    return authState.when(
      data: (user) {
        if (user != null) {
          return const HomePage(); // User is logged in
        }
        return const LoginPage(); // User is not logged in
      },
      loading:
          () =>
              const Scaffold(body: Center(child: CircularProgressIndicator())),
      error:
          (error, stackTrace) => Scaffold(
            body: Center(child: Text('Something went wrong: $error')),
          ),
    );
  }
}
