// Debug utility to log availability screen slot generation
// Use this in your main app to see what's happening with real data

import 'package:flutter/material.dart';

class AvailabilityDebugHelper {
  static void logSlotGeneration({
    required DateTime date,
    required String openTime,
    required String closeTime,
    required int slotDurationMinutes,
    required List<dynamic> bookings,
  }) {
    debugPrint('🔍 === AVAILABILITY DEBUG ===');
    debugPrint('📅 Date: $date');
    debugPrint('🕐 Open: $openTime, Close: $closeTime');
    debugPrint('⏱️ Slot Duration: $slotDurationMinutes minutes');
    debugPrint('📋 Bookings count: ${bookings.length}');

    // Log each booking
    for (int i = 0; i < bookings.length; i++) {
      final booking = bookings[i];
      if (booking is Map) {
        debugPrint(
          '  📌 Booking $i: ${booking['slot_start_time']} - ${booking['slot_end_time']} (Status: ${booking['status']})',
        );
      }
    }

    // Parse and calculate expected slots
    final openTimeParts = openTime.split(':');
    final closeTimeParts = closeTime.split(':');

    final openDateTime = DateTime(
      date.year,
      date.month,
      date.day,
      int.parse(openTimeParts[0]),
      int.parse(openTimeParts[1]),
    );

    final closeDateTime = DateTime(
      date.year,
      date.month,
      date.day,
      int.parse(closeTimeParts[0]),
      int.parse(closeTimeParts[1]),
    );

    debugPrint(
      '🕐 Parsed: ${openDateTime.toLocal()} - ${closeDateTime.toLocal()}',
    );

    final slotDuration = Duration(minutes: slotDurationMinutes);
    DateTime currentSlotStart = openDateTime;
    int slotIndex = 0;

    debugPrint('⏰ Expected slots:');
    while (currentSlotStart.isBefore(closeDateTime)) {
      final currentSlotEnd = currentSlotStart.add(slotDuration);
      if (currentSlotEnd.isAfter(closeDateTime)) {
        debugPrint('🛑 Slot would exceed closing time, stopping');
        break;
      }

      final timeString =
          '${currentSlotStart.hour.toString().padLeft(2, '0')}:${currentSlotStart.minute.toString().padLeft(2, '0')} - ${currentSlotEnd.hour.toString().padLeft(2, '0')}:${currentSlotEnd.minute.toString().padLeft(2, '0')}';
      debugPrint('  ⏰ Slot $slotIndex: $timeString');

      currentSlotStart = currentSlotEnd;
      slotIndex++;
    }

    debugPrint('📊 Total expected slots: $slotIndex');
    debugPrint('🔍 === END DEBUG ===');
  }
}

// Usage in availability screen or provider:
// AvailabilityDebugHelper.logSlotGeneration(
//   date: selectedDate,
//   openTime: pitchSettings.openTime,
//   closeTime: pitchSettings.closeTime,
//   slotDurationMinutes: pitchSettings.slotDurationMinutes,
//   bookings: rawBookingData,
// );
