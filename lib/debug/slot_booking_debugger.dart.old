// Debug script to help analyze the slot booking offset issue
// This file can be used to understand the relationship between database times and slot generation

import 'package:flutter/foundation.dart';

class SlotBookingDebugger {
  /// Debug function to analyze slot vs booking time differences
  static void analyzeSlotBookingOffset({
    required List<dynamic> bookings,
    required DateTime selectedDate,
    required String openTime,
    required String closeTime,
    required int slotDurationMinutes,
  }) {
    debugPrint('🔍 === SLOT BOOKING OFFSET ANALYSIS ===');
    debugPrint('📅 Selected Date: $selectedDate');
    debugPrint('🕐 Pitch Hours: $openTime - $closeTime');
    debugPrint('⏱️ Slot Duration: $slotDurationMinutes minutes');
    debugPrint('📊 Total Bookings: ${bookings.length}');
    debugPrint('');

    // Parse operating hours
    final openTimeParts = openTime.split(':');
    final closeTimeParts = closeTime.split(':');

    final openDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      int.parse(openTimeParts[0]),
      int.parse(openTimeParts[1]),
    );

    final closeDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      int.parse(closeTimeParts[0]),
      int.parse(closeTimeParts[1]),
    );

    // Generate expected slots
    final expectedSlots = <Map<String, dynamic>>[];
    DateTime currentSlotStart = openDateTime;
    int slotIndex = 0;

    while (currentSlotStart.isBefore(closeDateTime)) {
      final currentSlotEnd = currentSlotStart.add(
        Duration(minutes: slotDurationMinutes),
      );
      if (currentSlotEnd.isAfter(closeDateTime)) break;

      expectedSlots.add({
        'index': slotIndex,
        'start': currentSlotStart,
        'end': currentSlotEnd,
        'startLocal': currentSlotStart.toLocal(),
        'endLocal': currentSlotEnd.toLocal(),
      });

      currentSlotStart = currentSlotEnd;
      slotIndex++;
    }

    debugPrint('📋 Generated ${expectedSlots.length} expected slots:');
    for (final slot in expectedSlots) {
      debugPrint(
        '  Slot ${slot['index']}: ${_formatTime(slot['startLocal'])} - ${_formatTime(slot['endLocal'])}',
      );
    }
    debugPrint('');

    // Analyze each booking
    debugPrint('🔍 Booking Analysis:');
    for (int i = 0; i < bookings.length; i++) {
      final booking = bookings[i];
      if (booking is Map<String, dynamic>) {
        final bookingStart = DateTime.parse(booking['slot_start_time']);
        final bookingEnd = DateTime.parse(booking['slot_end_time']);

        debugPrint('📌 Booking $i (ID: ${booking['id']}):');
        debugPrint(
          '  🕐 Start: ${booking['slot_start_time']} (Local: ${bookingStart.toLocal()})',
        );
        debugPrint(
          '  🕐 End: ${booking['slot_end_time']} (Local: ${bookingEnd.toLocal()})',
        );
        debugPrint('  📊 Status: ${booking['status']}');

        // Find which slot this booking should match
        int? expectedSlotIndex;
        for (int j = 0; j < expectedSlots.length; j++) {
          final slot = expectedSlots[j];
          if ((slot['start'] as DateTime).isAtSameMomentAs(bookingStart) &&
              (slot['end'] as DateTime).isAtSameMomentAs(bookingEnd)) {
            expectedSlotIndex = j;
            break;
          }
        }

        if (expectedSlotIndex != null) {
          debugPrint('  ✅ Matches expected slot $expectedSlotIndex');
        } else {
          debugPrint('  ❌ NO MATCHING SLOT FOUND!');
          debugPrint('  🔍 Checking for close matches:');

          for (int j = 0; j < expectedSlots.length; j++) {
            final slot = expectedSlots[j];
            final slotStart = slot['start'] as DateTime;
            final slotEnd = slot['end'] as DateTime;

            final startDiff = bookingStart.difference(slotStart).inMinutes;
            final endDiff = bookingEnd.difference(slotEnd).inMinutes;

            if (startDiff.abs() <= 5 || endDiff.abs() <= 5) {
              // Within 5 minutes
              debugPrint(
                '    🔧 Close to slot $j: start diff ${startDiff}min, end diff ${endDiff}min',
              );
            }
          }
        }
        debugPrint('');
      }
    }

    debugPrint('🎯 === OFFSET ANALYSIS COMPLETE ===');
  }

  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
