// lib/core/services/time_service.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for the TimeService.
///
/// This provider allows the TimeService to be injected throughout the app
/// and easily mocked in tests.
///
/// Example usage:
/// ```dart
/// final timeService = ref.watch(timeServiceProvider);
/// final currentTime = timeService.now();
/// ```
final timeServiceProvider = Provider<TimeService>((ref) {
  return TimeService();
});

/// Centralized time service for standardized DateTime handling across the app.
///
/// This service provides:
/// 1. Mockable current time for testing
/// 2. Standardized date range calculations
/// 3. Timezone-aware date operations
/// 4. Consistent date formatting utilities
///
/// The service is designed to be injected via Riverpod providers to enable
/// easy mocking in tests and consistent time handling throughout the app.
class TimeService {
  /// Get the current time.
  ///
  /// This method allows for easy mocking in tests by overriding the
  /// time source. In production, it returns DateTime.now().
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final currentTime = timeService.now();
  /// ```
  DateTime now() => DateTime.now();

  /// Get the current date (without time component).
  ///
  /// Returns the current date with time set to midnight (00:00:00).
  /// Useful for date-only comparisons and operations.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final today = timeService.today();
  /// // Returns: DateTime(2025, 7, 9, 0, 0, 0)
  /// ```
  DateTime today() {
    final now = this.now();
    return DateTime(now.year, now.month, now.day);
  }

  /// Create a standardized date range for a specific date.
  ///
  /// This method creates a DateTimeRange that spans the entire day
  /// in UTC timezone to ensure consistent database queries regardless
  /// of the user's local timezone.
  ///
  /// **Critical Fix**: This addresses the timezone issues documented
  /// in the migration plan by ensuring all date ranges are in UTC.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final date = DateTime(2025, 7, 9);
  /// final range = timeService.dateRangeForDate(date);
  /// // Returns: DateTimeRange(
  /// //   start: 2025-07-09T00:00:00.000Z,
  /// //   end: 2025-07-09T23:59:59.999Z
  /// // )
  /// ```
  DateTimeRange dateRangeForDate(DateTime date) {
    // Convert to UTC to ensure consistent database queries
    final utcDate = DateTime.utc(date.year, date.month, date.day);

    return DateTimeRange(
      start: utcDate, // Start of day in UTC (00:00:00.000Z)
      end: utcDate
          .add(const Duration(days: 1))
          .subtract(
            const Duration(milliseconds: 1),
          ), // End of day in UTC (23:59:59.999Z)
    );
  }

  /// Get the start of day for a given date in UTC.
  ///
  /// Returns the given date with time set to 00:00:00.000 in UTC.
  /// This is useful for database queries that need consistent timezone handling.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final date = DateTime(2025, 7, 9, 15, 30); // Local time
  /// final startOfDay = timeService.startOfDayUtc(date);
  /// // Returns: 2025-07-09T00:00:00.000Z
  /// ```
  DateTime startOfDayUtc(DateTime date) {
    return DateTime.utc(date.year, date.month, date.day);
  }

  /// Get the end of day for a given date in UTC.
  ///
  /// Returns the given date with time set to 23:59:59.999 in UTC.
  /// This is useful for database queries that need consistent timezone handling.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final date = DateTime(2025, 7, 9, 15, 30); // Local time
  /// final endOfDay = timeService.endOfDayUtc(date);
  /// // Returns: 2025-07-09T23:59:59.999Z
  /// ```
  DateTime endOfDayUtc(DateTime date) {
    return DateTime.utc(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Check if two dates are on the same calendar day.
  ///
  /// This method compares only the date components (year, month, day)
  /// and ignores the time components and timezone differences.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final date1 = DateTime(2025, 7, 9, 10, 30);
  /// final date2 = DateTime(2025, 7, 9, 15, 45);
  /// final isSameDay = timeService.isSameDay(date1, date2);
  /// // Returns: true
  /// ```
  bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// Check if a date is today.
  ///
  /// Compares the given date with the current date, ignoring time components.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final someDate = DateTime(2025, 7, 9, 15, 30);
  /// final isToday = timeService.isToday(someDate);
  /// // Returns: true (if current date is 2025-07-09)
  /// ```
  bool isToday(DateTime date) {
    return isSameDay(date, today());
  }

  /// Check if a date is in the future (after today).
  ///
  /// Compares only the date components, ignoring time.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final futureDate = DateTime(2025, 7, 10);
  /// final isFuture = timeService.isFutureDate(futureDate);
  /// // Returns: true (if current date is 2025-07-09)
  /// ```
  bool isFutureDate(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final todayOnly = today();
    return dateOnly.isAfter(todayOnly);
  }

  /// Check if a date is in the past (before today).
  ///
  /// Compares only the date components, ignoring time.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final pastDate = DateTime(2025, 7, 8);
  /// final isPast = timeService.isPastDate(pastDate);
  /// // Returns: true (if current date is 2025-07-09)
  /// ```
  bool isPastDate(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final todayOnly = today();
    return dateOnly.isBefore(todayOnly);
  }

  /// Convert a local DateTime to UTC for database storage.
  ///
  /// This ensures consistent timezone handling when storing dates
  /// in the database. All database operations should use UTC.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final localTime = DateTime(2025, 7, 9, 15, 30); // Local time
  /// final utcTime = timeService.toUtcForStorage(localTime);
  /// ```
  DateTime toUtcForStorage(DateTime localDateTime) {
    return localDateTime.toUtc();
  }

  /// Convert a UTC DateTime from database to local time for display.
  ///
  /// This ensures dates are displayed in the user's local timezone
  /// while maintaining UTC storage in the database.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final utcTime = DateTime.utc(2025, 7, 9, 13, 30); // From database
  /// final localTime = timeService.toLocalForDisplay(utcTime);
  /// ```
  DateTime toLocalForDisplay(DateTime utcDateTime) {
    return utcDateTime.toLocal();
  }

  /// Get a grace period adjusted time for booking validation.
  ///
  /// This method subtracts a grace period from the current time,
  /// which is used for validating if slots can still be booked.
  ///
  /// Example:
  /// ```dart
  /// final timeService = TimeService();
  /// final cutoffTime = timeService.getBookingCutoffTime();
  /// // Returns: current time minus 5 minutes
  /// ```
  DateTime getBookingCutoffTime({
    Duration gracePeriod = const Duration(minutes: 5),
  }) {
    return now().subtract(gracePeriod);
  }
}
