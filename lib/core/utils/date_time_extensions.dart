// lib/core/utils/date_time_extensions.dart
import 'package:flutter/material.dart';

extension DateTimeExtensions on DateTime {
  /// Returns a new DateTime object with the time set to 00:00:00.
  DateTime get startOfDay => DateTime(year, month, day);

  /// Returns a new DateTime object with the time set to 23:59:59.
  DateTime get endOfDay => DateTime(year, month, day, 23, 59, 59);

  /// Converts a DateTime object to TimeOfDay.
  TimeOfDay get timeOfDay => TimeOfDay(hour: hour, minute: minute);
}
