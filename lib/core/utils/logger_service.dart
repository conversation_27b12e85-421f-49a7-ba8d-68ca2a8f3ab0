import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart'; // For kReleaseMode

class AppLogger {
  static final AppLogger _instance = AppLogger._internal();
  late Logger _logger;

  factory AppLogger() {
    return _instance;
  }

  AppLogger._internal() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 1, // Number of method calls to be displayed
        errorMethodCount: 8, // Number of method calls if stacktrace is provided
        lineLength: 120, // Width of the output
        colors: true, // Colorful log messages
        printEmojis: true, // Print an emoji for each log message
        // printTime: true, // Should each log print contain a timestamp (Removed as it's deprecated and timestamps are on by default)
      ),
      // Only log warnings and errors in release mode
      // and all levels in debug mode.
      level: kReleaseMode ? Level.warning : Level.debug,
    );
  }

  // Define methods for each log level
  void d(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  void i(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  void w(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  void e(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (error is StackTrace) {
      // If error is actually a StackTrace, move it to stackTrace parameter
      _logger.e(message, stackTrace: error);
    } else {
      _logger.e(message, error: error, stackTrace: stackTrace);
    }
  }

  void v(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.t(
      message,
      error: error,
      stackTrace: stackTrace,
    ); // 't' is for trace/verbose in logger package
  }

  void wtf(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(
      message,
      error: error,
      stackTrace: stackTrace,
    ); // 'f' is for fatal/wtf in logger package
  }
}

// Global instance for easy access
final logger = AppLogger();
