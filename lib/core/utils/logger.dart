import 'package:flutter/foundation.dart';

class AppLogger {
  static void log(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String level = "INFO",
  }) {
    if (kDebugMode) {
      print('$level: $message');
      if (error != null) {
        print('ERROR_DETAIL: $error');
      }
      if (stackTrace != null) {
        print('STACKTRACE: $stackTrace');
      }
    }
  }

  void d(String message) {
    // Debug
    AppLogger.log(message, level: "DEBUG");
  }

  void i(String message) {
    // Info
    AppLogger.log(message, level: "INFO");
  }

  void w(String message, {Object? error, StackTrace? stackTrace}) {
    // Warning
    AppLogger.log(
      message,
      error: error,
      stackTrace: stackTrace,
      level: "WARNING",
    );
  }

  void e(String message, {Object? error, StackTrace? stackTrace}) {
    // Error
    AppLogger.log(
      message,
      error: error,
      stackTrace: stackTrace,
      level: "ERROR",
    );
  }

  // Keep original methods if they were used elsewhere, or remove if d,i,w,e are sufficient
  void info(String message) {
    // Alias for i
    i(message);
  }

  void warning(String message, {Object? error, StackTrace? stackTrace}) {
    // Alias for w
    w(message, error: error, stackTrace: stackTrace);
  }

  void error(String message, {Object? error, StackTrace? stackTrace}) {
    // Alias for e
    e(message, error: error, stackTrace: stackTrace);
  }
}

// Global logger instance
final logger = AppLogger();
