// lib/core/utils/date_formatters.dart
import 'package:intl/intl.dart';

class AppDateFormats {
  static final DateFormat dayMonthYear = DateFormat('EEE, MMM d, yyyy');
  static final DateFormat hourMinute = DateFormat('HH:mm');
  static final DateFormat yearMonthDay = DateFormat('yyyy-MM-dd');
  static final DateFormat fullDate = DateFormat('EEEE, MMMM d, yyyy');
  static final DateFormat shortTime =
      DateFormat.jm(); // For locale-specific HH:mm AM/PM

  // Add other common formats as needed
}
