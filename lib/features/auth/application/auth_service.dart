import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Provides the Supabase client instance
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

// Provides the authentication repository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return AuthRepository(client);
});

// Provides the authentication service
final authServiceProvider = Provider<AuthService>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return AuthService(authRepository);
});

// Provides a stream of authentication state changes
final authStateChangesProvider = StreamProvider<User?>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.authStateChanges;
});

// Provides the current user (null if not authenticated)
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateChangesProvider);
  return authState.asData?.value;
});

class AuthRepository {
  final SupabaseClient _client;

  AuthRepository(this._client);

  Stream<User?> get authStateChanges => _client.auth.onAuthStateChange.map(
    (authState) => authState.session?.user,
  );

  User? get currentUser => _client.auth.currentUser;

  Future<void> signUpWithEmailPassword(String email, String password) async {
    try {
      await _client.auth.signUp(email: email, password: password);
    } on AuthException catch (e) {
      // Consider more specific error handling or re-throwing custom errors
      throw Exception('Failed to sign up: ${e.message}');
    } catch (e) {
      throw Exception('An unexpected error occurred during sign up.');
    }
  }

  Future<void> signInWithEmailPassword(String email, String password) async {
    try {
      await _client.auth.signInWithPassword(email: email, password: password);
    } on AuthException catch (e) {
      throw Exception('Failed to sign in: ${e.message}');
    } catch (e) {
      throw Exception('An unexpected error occurred during sign in.');
    }
  }

  // Add this method for Magic Link (OTP)
  Future<void> signInWithOtp(String email, String? emailRedirectTo) async {
    try {
      await _client.auth.signInWithOtp(
        email: email,
        emailRedirectTo: emailRedirectTo, // Make sure this is configured
      );
    } on AuthException catch (e) {
      throw Exception('Failed to send OTP: ${e.message}');
    } catch (e) {
      throw Exception('An unexpected error occurred while sending OTP.');
    }
  }

  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: ${e.toString()}');
    }
  }

  // TODO: Add other auth methods as needed (e.g., password reset, social login)
}

class AuthService {
  final AuthRepository _authRepository;

  AuthService(this._authRepository);

  Stream<User?> get authStateChanges => _authRepository.authStateChanges;
  User? get currentUser => _authRepository.currentUser;

  Future<void> signUpWithEmailPassword(String email, String password) =>
      _authRepository.signUpWithEmailPassword(email, password);

  Future<void> signInWithEmailPassword(String email, String password) =>
      _authRepository.signInWithEmailPassword(email, password);

  // Add this method for Magic Link (OTP)
  Future<void> signInWithOtp(String email, String? emailRedirectTo) =>
      _authRepository.signInWithOtp(email, emailRedirectTo);

  Future<void> signOut() => _authRepository.signOut();
}
