// lib/features/booking/presentation/my_bookings_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/booking/data/booking_repository.dart'; // Provider for fetching bookings
import 'package:skillz/features/booking/domain/booking_model.dart'; // Import for BookingStatus enum
// import 'package:skillz/core/utils/logger.dart'; // If you need logging
import 'package:skillz/core/utils/date_formatters.dart'; // Updated import

class MyBookingsScreen extends ConsumerWidget {
  const MyBookingsScreen({super.key});

  static const routeName = 'my-bookings';
  static const routePath = '/my-bookings';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bookingsAsyncValue = ref.watch(userBookingsProvider);
    final now = DateTime.now();

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('My Bookings'),
          bottom: const TabBar(
            tabs: [
              Tab(
                icon: Icon(Icons.event_available), // Upcoming bookings icon
                text: 'Upcoming',
              ),
              Tab(
                icon: Icon(Icons.history), // Past bookings icon
                text: 'Past',
              ),
            ],
          ),
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(userBookingsProvider);
          },
          child: bookingsAsyncValue.when(
            data: (bookings) {
              final upcoming =
                  bookings.where((b) => b.slotEndTime.isAfter(now)).toList();
              final past =
                  bookings.where((b) => b.slotEndTime.isBefore(now)).toList();
              return TabBarView(
                children: [
                  _BookingsList(
                    bookings: upcoming,
                    emptyMessage: 'No upcoming bookings.',
                  ),
                  _BookingsList(
                    bookings: past,
                    emptyMessage: 'No past bookings.',
                  ),
                ],
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error:
                (err, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red[300],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Error fetching bookings: $err',
                        style: TextStyle(color: Colors.red[400], fontSize: 16),
                      ),
                      const SizedBox(height: 12),
                      ElevatedButton(
                        onPressed: () => ref.invalidate(userBookingsProvider),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
          ),
        ),
      ),
    );
  }
}

class _BookingsList extends StatelessWidget {
  final List bookings;
  final String emptyMessage;

  const _BookingsList({required this.bookings, required this.emptyMessage});

  @override
  Widget build(BuildContext context) {
    if (bookings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_busy_outlined, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 12),
            Text(
              emptyMessage,
              style: TextStyle(color: Colors.grey[600], fontSize: 16),
            ),
          ],
        ),
      );
    }
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      separatorBuilder: (_, __) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final booking = bookings[index];
        final slotStart = booking.slotStartTime;
        final slotEnd = booking.slotEndTime;
        final status = _getStatusString(booking.status);
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.sports_soccer, size: 32, color: Colors.blue[700]),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Pitch: ${booking.pitchId}',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Date: ${AppDateFormats.dayMonthYear.format(slotStart)}', // Updated usage
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                      Text(
                        'Time: ${AppDateFormats.hourMinute.format(slotStart)} - ${AppDateFormats.hourMinute.format(slotEnd)}', // Updated usage
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Chip(
                            label: Text(status),
                            backgroundColor: _getStatusColor(status),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              // TODO: Implement booking cancellation
                            },
                            icon: Icon(Icons.cancel, color: Colors.red[700]),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getStatusString(BookingStatus status) {
    switch (status) {
      case BookingStatus.pendingPayment:
        return 'Pending Payment';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.cancelledByUser:
        return 'Cancelled by User';
      case BookingStatus.cancelledByAdmin:
        return 'Cancelled by Admin';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.noShow:
        return 'No Show';
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return Colors.green[300]!;
      case 'pendingpayment':
        return Colors.orange[300]!;
      case 'cancelledbyuser':
      case 'cancelledbyadmin':
        return Colors.red[300]!;
      case 'completed':
        return Colors.blue[300]!;
      case 'noshow':
        return Colors.grey[600]!;
      default:
        return Colors.grey[400]!;
    }
  }
}

// REMOVE THE DUPLICATE DEFINITION FROM HERE
// final userBookingsProvider = FutureProvider<List<Booking>>((ref) async {
//   final bookingRepository = ref.watch(bookingRepositoryProvider);
//   return bookingRepository.fetchUserBookings();
// });
