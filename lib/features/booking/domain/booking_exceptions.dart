// lib/features/booking/domain/booking_exceptions.dart

/// Base class for all booking-related exceptions
abstract class BookingException implements Exception {
  const BookingException(this.message);

  final String message;

  @override
  String toString() => 'BookingException: $message';
}

/// Exception thrown when a user tries to book a slot that's already unavailable
class SlotUnavailableException extends BookingException {
  const SlotUnavailableException(super.message);

  @override
  String toString() => 'SlotUnavailableException: $message';
}

/// Exception thrown when a user reaches their booking limit
class BookingLimitReachedException extends BookingException {
  const BookingLimitReachedException(super.message);

  @override
  String toString() => 'BookingLimitReachedException: $message';
}

/// Exception thrown when another user books the same slot simultaneously
class BookingConflictException extends BookingException {
  const BookingConflictException(super.message);

  @override
  String toString() => 'BookingConflictException: $message';
}

/// Exception thrown when trying to book a slot in the past
class PastSlotBookingException extends BookingException {
  const PastSlotBookingException(super.message);

  @override
  String toString() => 'PastSlotBookingException: $message';
}

/// Exception thrown when pitch settings are invalid or not found
class InvalidPitchException extends BookingException {
  const InvalidPitchException(super.message);

  @override
  String toString() => 'InvalidPitchException: $message';
}

/// Exception thrown for general booking failures that don't fit other categories
class GeneralBookingFailureException extends BookingException {
  const GeneralBookingFailureException(super.message);

  @override
  String toString() => 'GeneralBookingFailureException: $message';
}

/// Exception thrown when user is not authenticated for booking operations
class UserNotAuthenticatedException extends BookingException {
  const UserNotAuthenticatedException(super.message);

  @override
  String toString() => 'UserNotAuthenticatedException: $message';
}
