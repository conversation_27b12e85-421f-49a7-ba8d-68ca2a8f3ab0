// lib/features/booking/domain/booking_business_rules.dart

import 'booking_model.dart';

/// Centralized business rules for booking-related operations.
/// 
/// This class consolidates all business logic that was previously scattered
/// across UI components and data providers, ensuring consistency and
/// maintainability.
/// 
/// All methods are static and pure functions to ensure testability and
/// prevent side effects.
class BookingBusinessRules {
  // Private constructor to prevent instantiation
  BookingBusinessRules._();

  /// Determines if a booking should be considered "upcoming".
  /// 
  /// A booking is considered upcoming if its start time is after the current time.
  /// This handles the edge case where slotStartTime == currentTime by treating
  /// it as NOT upcoming (i.e., it's considered current/active).
  /// 
  /// **Business Rule**: `slotStartTime > currentTime`
  /// 
  /// Example:
  /// ```dart
  /// final booking = Booking(slotStartTime: DateTime(2025, 7, 9, 15, 0));
  /// final currentTime = DateTime(2025, 7, 9, 14, 30);
  /// final isUpcoming = BookingBusinessRules.isUpcomingBooking(booking, currentTime);
  /// // Returns: true
  /// ```
  static bool isUpcomingBooking(Booking booking, DateTime currentTime) {
    return booking.slotStartTime.isAfter(currentTime);
  }

  /// Determines if a booking should be considered "past".
  /// 
  /// A booking is considered past if its start time is before the current time.
  /// This handles the edge case where slotStartTime == currentTime by treating
  /// it as NOT past (i.e., it's considered current/active).
  /// 
  /// **Business Rule**: `slotStartTime < currentTime`
  /// 
  /// Example:
  /// ```dart
  /// final booking = Booking(slotStartTime: DateTime(2025, 7, 9, 13, 0));
  /// final currentTime = DateTime(2025, 7, 9, 14, 30);
  /// final isPast = BookingBusinessRules.isPastBooking(booking, currentTime);
  /// // Returns: true
  /// ```
  static bool isPastBooking(Booking booking, DateTime currentTime) {
    return booking.slotStartTime.isBefore(currentTime);
  }

  /// Determines if a booking should be considered "current/active".
  /// 
  /// A booking is considered current if its start time equals the current time.
  /// This handles the edge case that was previously falling through filters.
  /// 
  /// **Business Rule**: `slotStartTime == currentTime`
  /// 
  /// Example:
  /// ```dart
  /// final booking = Booking(slotStartTime: DateTime(2025, 7, 9, 14, 30));
  /// final currentTime = DateTime(2025, 7, 9, 14, 30);
  /// final isCurrent = BookingBusinessRules.isCurrentBooking(booking, currentTime);
  /// // Returns: true
  /// ```
  static bool isCurrentBooking(Booking booking, DateTime currentTime) {
    return booking.slotStartTime.isAtSameMomentAs(currentTime);
  }

  /// Determines if a booking should count toward the active booking limit.
  /// 
  /// This method aligns with the backend logic by using the slot end time
  /// instead of start time. A booking counts toward the limit if:
  /// 1. Its end time is after the current time (still active/future)
  /// 2. It has not been cancelled
  /// 
  /// **Business Rule**: `slotEndTime > currentTime AND status != cancelled`
  /// 
  /// **Critical Fix**: This resolves the frontend/backend inconsistency where
  /// frontend was using slotStartTime and backend was using slotEndTime.
  /// 
  /// Example:
  /// ```dart
  /// final booking = Booking(
  ///   slotStartTime: DateTime(2025, 7, 9, 13, 0),
  ///   slotEndTime: DateTime(2025, 7, 9, 14, 0),
  ///   status: BookingStatus.confirmed,
  /// );
  /// final currentTime = DateTime(2025, 7, 9, 13, 30);
  /// final countsTowardLimit = BookingBusinessRules.countsTowardBookingLimit(booking, currentTime);
  /// // Returns: true (booking is still active until 14:00)
  /// ```
  static bool countsTowardBookingLimit(Booking booking, DateTime currentTime) {
    // Check if booking is still active (end time is in the future)
    final isStillActive = booking.slotEndTime.isAfter(currentTime);
    
    // Check if booking is not cancelled
    final isNotCancelled = !isCancelledBooking(booking);
    
    return isStillActive && isNotCancelled;
  }

  /// Determines if a booking has been cancelled.
  /// 
  /// A booking is considered cancelled if its status is either
  /// cancelledByUser or cancelledByAdmin.
  /// 
  /// **Business Rule**: `status IN (cancelledByUser, cancelledByAdmin)`
  /// 
  /// Example:
  /// ```dart
  /// final booking = Booking(status: BookingStatus.cancelledByUser);
  /// final isCancelled = BookingBusinessRules.isCancelledBooking(booking);
  /// // Returns: true
  /// ```
  static bool isCancelledBooking(Booking booking) {
    return booking.status == BookingStatus.cancelledByUser ||
           booking.status == BookingStatus.cancelledByAdmin;
  }

  /// Determines if a booking has an active status for business purposes.
  /// 
  /// A booking is considered active if it's confirmed or pending payment.
  /// Cancelled, completed, and no-show bookings are not considered active.
  /// 
  /// **Business Rule**: `status IN (confirmed, pendingPayment)`
  /// 
  /// Example:
  /// ```dart
  /// final booking = Booking(status: BookingStatus.confirmed);
  /// final isActive = BookingBusinessRules.isActiveBooking(booking);
  /// // Returns: true
  /// ```
  static bool isActiveBooking(Booking booking) {
    return booking.status == BookingStatus.confirmed ||
           booking.status == BookingStatus.pendingPayment;
  }

  /// Determines if a slot can be booked based on time constraints.
  /// 
  /// A slot can be booked if its start time is after the current time
  /// minus a grace period. This aligns with the backend validation logic
  /// that prevents booking slots too far in the past.
  /// 
  /// **Business Rule**: `slotStartTime > (currentTime - gracePeriod)`
  /// **Default Grace Period**: 5 minutes (aligns with backend)
  /// 
  /// Example:
  /// ```dart
  /// final slotStartTime = DateTime(2025, 7, 9, 14, 25);
  /// final currentTime = DateTime(2025, 7, 9, 14, 30);
  /// final canBook = BookingBusinessRules.isSlotBookable(slotStartTime, currentTime);
  /// // Returns: true (within 5-minute grace period)
  /// ```
  static bool isSlotBookable(
    DateTime slotStartTime, 
    DateTime currentTime, {
    Duration gracePeriod = const Duration(minutes: 5),
  }) {
    final cutoffTime = currentTime.subtract(gracePeriod);
    return slotStartTime.isAfter(cutoffTime);
  }

  /// Filters a list of bookings to return only upcoming ones.
  /// 
  /// This is a convenience method that applies the isUpcomingBooking rule
  /// to a collection of bookings.
  /// 
  /// Example:
  /// ```dart
  /// final allBookings = [booking1, booking2, booking3];
  /// final currentTime = DateTime.now();
  /// final upcoming = BookingBusinessRules.filterUpcomingBookings(allBookings, currentTime);
  /// ```
  static List<Booking> filterUpcomingBookings(
    List<Booking> bookings, 
    DateTime currentTime,
  ) {
    return bookings.where((booking) => isUpcomingBooking(booking, currentTime)).toList();
  }

  /// Filters a list of bookings to return only past ones.
  /// 
  /// This is a convenience method that applies the isPastBooking rule
  /// to a collection of bookings.
  /// 
  /// Example:
  /// ```dart
  /// final allBookings = [booking1, booking2, booking3];
  /// final currentTime = DateTime.now();
  /// final past = BookingBusinessRules.filterPastBookings(allBookings, currentTime);
  /// ```
  static List<Booking> filterPastBookings(
    List<Booking> bookings, 
    DateTime currentTime,
  ) {
    return bookings.where((booking) => isPastBooking(booking, currentTime)).toList();
  }

  /// Counts bookings that contribute to the booking limit.
  /// 
  /// This is a convenience method that applies the countsTowardBookingLimit rule
  /// to a collection of bookings and returns the count.
  /// 
  /// Example:
  /// ```dart
  /// final allBookings = [booking1, booking2, booking3];
  /// final currentTime = DateTime.now();
  /// final count = BookingBusinessRules.countActiveBookings(allBookings, currentTime);
  /// ```
  static int countActiveBookings(
    List<Booking> bookings, 
    DateTime currentTime,
  ) {
    return bookings.where((booking) => countsTowardBookingLimit(booking, currentTime)).length;
  }
}
