// lib/features/booking/data/booking_repository.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/booking/domain/booking_exceptions.dart'; // Import custom exceptions
import 'package:skillz/core/utils/logger_service.dart'; // Import the logger service
import 'package:skillz/features/auth/application/auth_service.dart'; // Import auth service for supabaseClientProvider
import 'package:skillz/features/availability/domain/pitch_settings_model.dart'; // Import for dynamic settings

final bookingRepositoryProvider = Provider<BookingRepository>((ref) {
  return BookingRepository(ref.watch(supabaseClientProvider));
});

// Define userBookingsProvider here
final userBookingsProvider = FutureProvider<List<Booking>>((ref) async {
  final bookingRepository = ref.watch(bookingRepositoryProvider);
  return bookingRepository.fetchUserBookings();
});

class BookingRepository {
  final SupabaseClient _client;
  final logger = AppLogger(); // Create a logger instance

  BookingRepository(this._client);

  Future<void> createBooking({
    // userId is derived from the authenticated user session by Supabase
    required int pitchId,
    required DateTime slotStartTime,
    required DateTime slotEndTime,
  }) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        // This should ideally be caught earlier in the UI/service layer
        throw Exception('User not authenticated. Cannot create booking.');
      }

      // Fetch pitch settings to get dynamic booking limit
      final pitchSettingsResponse =
          await _client
              .from('pitch_settings')
              .select()
              .eq('id', pitchId)
              .single();

      final pitchSettings = PitchSettings.fromJson(pitchSettingsResponse);

      // Check for existing active bookings
      // Fetch the count of upcoming bookings for the user (slot_end_time in the future)
      final now = DateTime.now();
      final List<dynamic> upcomingBookingsResponse = await _client
          .from('bookings')
          .select('id, slot_end_time') // Select minimal fields
          .eq('user_id', currentUser.id)
          .eq('status', 'confirmed');

      final upcomingBookingsCount =
          upcomingBookingsResponse
              .where((b) => DateTime.parse(b['slot_end_time']).isAfter(now))
              .length;

      if (upcomingBookingsCount >= pitchSettings.maxBookingsPerUser) {
        throw BookingLimitReachedException(
          'You have reached the maximum of ${pitchSettings.maxBookingsPerUser} active bookings for this pitch.',
        );
      }

      // Check for conflicting bookings before creating the new booking
      await _checkForConflictingBookings(
        pitchId: pitchId,
        slotStartTime: slotStartTime,
        slotEndTime: slotEndTime,
      );

      final bookingData = {
        'user_id': currentUser.id, // RLS policies will also use auth.uid()
        'pitch_id': pitchId,
        'slot_start_time': slotStartTime.toIso8601String(),
        'slot_end_time': slotEndTime.toIso8601String(),
        'status': 'confirmed', // Default status as per schema
      };

      // The .insert() method will throw a PostgrestException if RLS fails
      // or if there's any other database error (e.g., constraint violation).
      await _client.from('bookings').insert(bookingData);

      // If you needed the created booking data back, you could use .select()
      // final response = await _client.from('bookings').insert(bookingData).select();
      // return Booking.fromJson(response[0]); // Assuming Booking model is set up
    } on PostgrestException catch (e, s) {
      // Log detailed error for debugging
      logger.e(
        'Supabase Error creating booking: ${e.message} (Code: ${e.code})',
        s,
      );

      // Check for specific database error codes that indicate conflicts
      if (e.code == '23505') {
        // Unique constraint violation - likely a double booking scenario
        throw SlotUnavailableException(
          'This time slot is already booked. Please select a different time.',
        );
      } else if (e.code == '23503') {
        // Foreign key constraint violation
        throw GeneralBookingFailureException(
          'Invalid pitch or user information. Please try again.',
        );
      } else if (e.message.toLowerCase().contains('overlap') ||
          e.message.toLowerCase().contains('conflict') ||
          e.message.toLowerCase().contains('duplicate')) {
        // Generic overlap/conflict detection
        throw SlotUnavailableException(
          'This time slot conflicts with an existing booking. Please choose a different time.',
        );
      }

      // Provide a more user-friendly message or rethrow a custom exception
      throw GeneralBookingFailureException(
        'Failed to create booking. Please try again.',
      );
    } catch (e, s) {
      logger.e('Unexpected error creating booking: $e', s);
      throw GeneralBookingFailureException(
        'An unexpected error occurred while creating your booking.',
      );
    }
  }

  /// Check for conflicting bookings in the same time slot
  /// This method can be called with an optional excludeUserId to avoid
  /// false positives when checking conflicts after a booking was already created
  Future<void> _checkForConflictingBookings({
    required int pitchId,
    required DateTime slotStartTime,
    required DateTime slotEndTime,
    String? excludeUserId,
  }) async {
    try {
      // Query for any confirmed bookings that would overlap with the new booking
      var query = _client
          .from('bookings')
          .select('id, slot_start_time, slot_end_time, user_id')
          .eq('pitch_id', pitchId)
          .eq('status', 'confirmed')
          // Check for overlapping time slots:
          // New booking overlaps if: start < existing_end AND end > existing_start
          .lt('slot_start_time', slotEndTime.toIso8601String())
          .gt('slot_end_time', slotStartTime.toIso8601String());

      // If excludeUserId is provided, exclude bookings by that user
      // This prevents false positives when the same user just created a booking
      if (excludeUserId != null) {
        query = query.neq('user_id', excludeUserId);
      }

      final response = await query;

      if (response.isNotEmpty) {
        logger.w(
          '[BookingRepository] Found ${response.length} conflicting booking(s) for pitch $pitchId at ${slotStartTime.toIso8601String()} - ${slotEndTime.toIso8601String()}${excludeUserId != null ? ' (excluding user $excludeUserId)' : ''}',
        );
        throw SlotUnavailableException(
          'Sorry, this time slot is already booked by another user. Please select a different time.',
        );
      }
    } on PostgrestException catch (e, s) {
      logger.e('Error checking for conflicting bookings: ${e.message}', s);
      // Re-throw as a more user-friendly error
      throw GeneralBookingFailureException(
        'Unable to verify slot availability. Please try again.',
      );
    }
  }

  // We will add fetchUserBookings() here later for the "My Bookings" screen
  Future<List<Booking>> fetchUserBookings() async {
    logger.i(
      '[BookingRepository] Attempting to fetch user bookings...',
    ); // Log initiation
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        logger.w(
          '[BookingRepository] fetchUserBookings: User not authenticated.',
        );
        throw Exception('User not authenticated. Cannot fetch bookings.');
      }
      logger.d(
        '[BookingRepository] fetchUserBookings: Fetching bookings for user ${currentUser.id}',
      );

      final response = await _client
          .from('bookings')
          .select() // Fetches all columns
          .eq('user_id', currentUser.id)
          .order('slot_start_time', ascending: true);

      // Log the raw response data
      logger.d(
        '[BookingRepository] fetchUserBookings: Raw response from Supabase: $response',
      );

      final List<Booking> bookings = [];
      for (final item in response) {
        try {
          bookings.add(Booking.fromJson(item));
        } catch (e, s) {
          // Log error and stack trace as part of the message string
          logger.e(
            '[BookingRepository] fetchUserBookings: Error parsing individual booking item: $item. Error: $e, StackTrace: $s',
          );
        }
      }

      logger.d(
        '[BookingRepository] fetchUserBookings: Successfully parsed ${bookings.length} bookings out of ${response.length} items received.',
      );
      return bookings;
    } on PostgrestException catch (e, s) {
      // Log error and stack trace as part of the message string
      logger.e(
        '[BookingRepository] Supabase PostgrestException fetching user bookings: ${e.message} (Code: ${e.code}, Details: ${e.details}). Error: $e, StackTrace: $s',
      );
      throw Exception(
        'Failed to fetch your bookings. Database error: ${e.message}',
      );
    } catch (e, s) {
      // Log error and stack trace as part of the message string
      logger.e(
        '[BookingRepository] Unexpected generic error fetching user bookings: ${e.toString()}. Error: $e, StackTrace: $s',
      );
      rethrow;
    }
  }
}
