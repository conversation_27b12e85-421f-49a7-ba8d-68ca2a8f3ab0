// lib/features/booking/application/booking_service.dart
//
// ⚠️  DEPRECATED: This service is deprecated as of June 13, 2025
//
// This legacy booking service has been replaced by OptimisticBookingService
// which implements ADR-006 requirements:
// - Race condition prevention via Edge Functions
// - Optimistic UI updates with rollback
// - Enhanced error handling
//
// Migration Path:
// OLD: bookingCreationProvider → NEW: optimisticBookingServiceProvider
// OLD: BookingCreationState   → NEW: OptimisticBookingState
// OLD: createBooking()        → NEW: createBookingOptimistic()
//
// For new development, use:
// import 'package:skillz/features/booking/application/optimistic_booking_service.dart';
//
// This file is kept temporarily for test compatibility only.
// TODO: Remove this file after test migration is complete (see ADR-006 cleanup notes)

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/booking/data/booking_repository.dart';

// Define states for the booking creation operation
@Deprecated(
  'Use OptimisticBookingState from optimistic_booking_service.dart instead',
)
abstract class BookingCreationState {
  const BookingCreationState();
}

@Deprecated(
  'Use OptimisticBookingState with OptimisticBookingStatus.idle instead',
)
class BookingCreationInitial extends BookingCreationState {}

@Deprecated(
  'Use OptimisticBookingState with OptimisticBookingStatus.optimisticLoading instead',
)
class BookingCreationLoading extends BookingCreationState {}

@Deprecated(
  'Use OptimisticBookingState with OptimisticBookingStatus.success instead',
)
class BookingCreationSuccess extends BookingCreationState {}

@Deprecated(
  'Use OptimisticBookingState with OptimisticBookingStatus.error instead',
)
class BookingCreationError extends BookingCreationState {
  final String message;
  final Exception? exception; // Store the original exception

  BookingCreationError(this.message, {this.exception});
}

// StateNotifier for managing the booking creation process
@Deprecated(
  'Use OptimisticBookingService from optimistic_booking_service.dart instead',
)
class BookingCreationNotifier extends StateNotifier<BookingCreationState> {
  final BookingRepository _bookingRepository;
  // You can also pass Ref to the notifier if it needs to read other providers
  // final Ref _ref;

  BookingCreationNotifier(this._bookingRepository /*, this._ref */)
    : super(BookingCreationInitial());

  Future<void> createBooking({
    required int pitchId, // Re-added pitchId parameter
    required DateTime slotStartTime,
    required DateTime slotEndTime,
  }) async {
    state = BookingCreationLoading();
    try {
      // The repository now implicitly uses the authenticated user's ID
      await _bookingRepository.createBooking(
        pitchId: pitchId, // Use the pitchId parameter
        slotStartTime: slotStartTime,
        slotEndTime: slotEndTime,
      );
      state = BookingCreationSuccess();
    } catch (e) {
      state = BookingCreationError(
        e.toString(),
        exception: e is Exception ? e : null,
      );
    }
  }

  // Call this to reset the state, e.g., after navigating away or showing a message
  void resetState() {
    state = BookingCreationInitial();
  }
}

// Provider for the BookingCreationNotifier
@Deprecated(
  'Use optimisticBookingServiceProvider from optimistic_booking_service.dart instead',
)
final bookingCreationProvider =
    StateNotifierProvider<BookingCreationNotifier, BookingCreationState>((ref) {
      final bookingRepository = ref.watch(bookingRepositoryProvider);
      return BookingCreationNotifier(bookingRepository);
    });

// NOTE: userBookingsProvider is now defined in booking_repository.dart to avoid duplication
