import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import 'package:skillz/core/utils/date_formatters.dart'; // Updated import
import 'package:skillz/core/utils/date_time_extensions.dart'; // Import DateTime extensions
import 'package:skillz/core/utils/logger.dart';
import './widgets/pitch_selection_dropdown.dart';
import './widgets/availability_details.dart';
import 'package:logging/logging.dart'; // Add this import

class AvailabilityScreen extends ConsumerStatefulWidget {
  final DateTime? initialDate;

  const AvailabilityScreen({super.key, this.initialDate});

  @override
  ConsumerState<AvailabilityScreen> createState() => _AvailabilityScreenState();
}

class _AvailabilityScreenState extends ConsumerState<AvailabilityScreen> {
  DateTime _selectedDate = DateTime.now().startOfDay;

  // Add a logger instance
  final Logger _logger = Logger('AvailabilityScreen');

  @override
  void initState() {
    super.initState();
    _selectedDate =
        widget.initialDate ??
        DateTime.now().startOfDay; // Use DateTime.now() as normal
    logger.d(
      '[AvailabilityScreen] initState: _selectedDate initialized to: $_selectedDate',
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final now = DateTime.now();
    DateTime currentSelectedDate = _selectedDate;
    final DateTime firstDateForPicker = now.startOfDay;

    // Ensure initialDate for the picker is not before firstDateForPicker
    if (currentSelectedDate.isBefore(firstDateForPicker)) {
      currentSelectedDate = firstDateForPicker;
    }

    _logger.fine(
      'DEBUG: initialDate: $currentSelectedDate, firstDateForPicker: $firstDateForPicker, now: $now',
    );

    _logger.fine(
      'ShowDatePicker called with: initialDate: $currentSelectedDate, firstDate: $firstDateForPicker',
    );

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate:
          currentSelectedDate, // Use the potentially adjusted currentSelectedDate
      firstDate: firstDateForPicker,
      lastDate: now.add(const Duration(days: 365)),
    );
    if (pickedDate != null && pickedDate != _selectedDate) {
      logger.i('[AvailabilityScreen] Date selected via picker: $pickedDate');

      // Additional safety check: ensure picked date is not in the past
      final today = DateTime.now().startOfDay;
      if (pickedDate.isBefore(today)) {
        logger.w(
          '[AvailabilityScreen] Picked date $pickedDate is in the past, using today $today instead',
        );
        setState(() {
          _selectedDate = today;
        });
      } else {
        setState(() {
          _selectedDate = pickedDate;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final allPitchesAsync = ref.watch(allPitchesProvider);
    final selectedPitchId = ref.watch(selectedPitchIdProvider);

    ref.listen<int?>(selectedPitchIdProvider, (prev, next) {
      if (prev != next) {
        logger.d(
          '[AvailabilityScreen] selectedPitchId changed from $prev to $next.',
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('View Availability'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () => _selectDate(context),
          ),
        ],
      ),
      body: GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity == null) return;
          if (details.primaryVelocity! < 0) {
            setState(() {
              _selectedDate = _selectedDate.add(const Duration(days: 1));
            });
            logger.i(
              '[AvailabilityScreen] Swiped left. New _selectedDate: $_selectedDate',
            );
          } else if (details.primaryVelocity! > 0) {
            final today =
                DateTime.now().startOfDay; // Use DateTime.now() as normal
            if (_selectedDate.isAfter(today)) {
              setState(() {
                _selectedDate = _selectedDate.subtract(const Duration(days: 1));
              });
              logger.i(
                '[AvailabilityScreen] Swiped right. New _selectedDate: $_selectedDate',
              );
            }
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Center(
                child: Text(
                  'Selected Date: ${AppDateFormats.fullDate.format(_selectedDate.toLocal())}', // Updated usage
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.black),
                ),
              ),
            ),
            allPitchesAsync.when(
              data: (pitches) {
                if (pitches.isEmpty) {
                  return const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text("No pitches available."),
                  );
                }
                return PitchSelectionDropdown(
                  pitches: pitches,
                  selectedPitchId: selectedPitchId,
                );
              },
              loading:
                  () => const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Center(child: CircularProgressIndicator()),
                  ),
              error:
                  (err, stack) => Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text("Error loading pitches: \\${err.toString()}"),
                  ),
            ),
            if (selectedPitchId == null)
              const Expanded(
                child: Center(
                  child: Text("Please select a pitch to see availability."),
                ),
              )
            else
              Expanded(
                child: AvailabilityDetails(
                  selectedDate: _selectedDate,
                  selectedPitchId: selectedPitchId,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
