import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:skillz/features/availability/domain/time_slot_info_model.dart';
import 'package:skillz/features/booking/data/booking_repository.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import 'package:skillz/features/availability/application/real_time_availability_provider.dart'
    as real_time;
import 'package:skillz/core/utils/logger_service.dart' as app_logger;
import 'package:skillz/features/booking/domain/booking_model.dart'
    as domain_booking; // For BookingStatus - use main booking model
import 'package:skillz/core/utils/date_formatters.dart'; // Added import

class SlotListItem extends ConsumerWidget {
  final TimeSlotInfo slot;
  final DateTime selectedDate;
  final int selectedPitchId;

  const SlotListItem({
    super.key,
    required this.slot,
    required this.selectedDate,
    required this.selectedPitchId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userBookingsAsync = ref.watch(userBookingsProvider);
    final optimisticBookingState = ref.watch(
      real_time.optimisticBookingStateProvider,
    );

    app_logger.logger.i(
      'SlotListItem build: userBookingsAsync state is ${userBookingsAsync.runtimeType}',
    );

    // Check if this slot is optimistically booked
    final slotKey =
        '${slot.startTime.toIso8601String()}_${slot.endTime.toIso8601String()}';
    final isOptimisticallyBooked = optimisticBookingState[slotKey] ?? false;

    return userBookingsAsync.when(
      data: (bookings) {
        final upcomingBookingsCount =
            bookings
                .where(
                  (booking) =>
                      booking.slotStartTime.isAfter(DateTime.now()) &&
                      booking.status !=
                          domain_booking.BookingStatus.cancelledByUser &&
                      booking.status !=
                          domain_booking.BookingStatus.cancelledByAdmin,
                )
                .length;
        final bookingLimitReached = upcomingBookingsCount >= 4;

        app_logger.logger.i(
          'SlotListItem build: upcomingBookingsCount = $upcomingBookingsCount, bookingLimitReached = $bookingLimitReached',
        );

        // Since we now filter past slots at the data level,
        // we only need to handle booking limit and normal slot display
        if (bookingLimitReached) {
          app_logger.logger.i(
            'Booking limit reached. Displaying Limit Reached button.',
          );
          return Card(
            elevation: 2.0,
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
            child: ListTile(
              title: Text(
                // MODIFIED: Use AppDateFormats.shortTime
                '${AppDateFormats.shortTime.format(slot.startTime.toLocal())} - ${AppDateFormats.shortTime.format(slot.endTime.toLocal())}',
                style: const TextStyle(color: Colors.grey),
              ),
              subtitle: const Text(
                'Unavailable',
                style: TextStyle(color: Colors.grey),
              ),
              trailing: SizedBox(
                width: 120,
                child: ElevatedButton(
                  onPressed: () {
                    app_logger.logger.i(
                      'Limit Reached button tapped. Showing SnackBar.',
                    );
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text(
                          'You have reached the maximum limit of 4 active bookings.',
                        ),
                        action: SnackBarAction(
                          label: 'View Bookings',
                          onPressed: () {
                            app_logger.logger.i(
                              'View Bookings SnackBar action tapped.',
                            );
                            context.go('/my_bookings');
                          },
                        ),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[300],
                    disabledForegroundColor: Colors.grey.withValues(
                      alpha: 0.38,
                    ),
                    disabledBackgroundColor: Colors.grey.withValues(
                      alpha: 0.12,
                    ),
                  ),
                  child: Text(
                    'Limit ($upcomingBookingsCount/4)',
                    style: const TextStyle(
                      color: Colors.black87,
                      inherit: false,
                    ),
                  ),
                ),
              ),
            ),
          );
        } else if (slot.isBooked || isOptimisticallyBooked) {
          // Handle already booked slots or optimistically booked slots
          String subtitle = slot.isBooked ? 'Booked' : 'Booking...';
          Color cardColor =
              slot.isBooked ? Colors.red[50]! : Colors.orange[50]!;

          return Card(
            color: cardColor,
            elevation: 2.0,
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
            child: ListTile(
              title: Text(
                '${AppDateFormats.shortTime.format(slot.startTime.toLocal())} - ${AppDateFormats.shortTime.format(slot.endTime.toLocal())}',
                style: const TextStyle(color: Colors.grey),
              ),
              subtitle: Text(
                subtitle,
                style: const TextStyle(color: Colors.grey),
              ),
              trailing: ElevatedButton(
                onPressed: null, // Disabled for booked slots
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                  disabledForegroundColor: Colors.grey.withValues(alpha: 0.38),
                  disabledBackgroundColor: Colors.grey.withValues(alpha: 0.12),
                ),
                child: Text(
                  subtitle,
                  style: const TextStyle(color: Colors.grey, inherit: false),
                ),
              ),
            ),
          );
        } else {
          String buttonText = 'Book';
          Color buttonBackgroundColor = Theme.of(context).colorScheme.primary;
          Color buttonForegroundColor = Theme.of(context).colorScheme.onPrimary;
          Color buttonBorderColor =
              Theme.of(context).colorScheme.primaryContainer;
          double? buttonElevation = 2.0;
          Color? shadowColorForButton = Colors.black.withValues(alpha: 0.2);

          return Card(
            elevation: 2.0,
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
            child: ListTile(
              title: Text(
                // MODIFIED: Use AppDateFormats.shortTime
                '${AppDateFormats.shortTime.format(slot.startTime.toLocal())} - ${AppDateFormats.shortTime.format(slot.endTime.toLocal())}',
                style: const TextStyle(color: Colors.black),
              ),
              trailing: SizedBox(
                width: 120,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: buttonBackgroundColor,
                    foregroundColor: buttonForegroundColor,
                    elevation: buttonElevation,
                    shadowColor: shadowColorForButton,
                    side: BorderSide(color: buttonBorderColor, width: 1),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    textStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      inherit: false,
                    ),
                  ).copyWith(
                    elevation: WidgetStateProperty.resolveWith<double?>((
                      states,
                    ) {
                      if (states.contains(WidgetState.disabled)) return 0;
                      if (states.contains(WidgetState.pressed)) return 4.0;
                      return buttonElevation;
                    }),
                  ),
                  onPressed: () async {
                    app_logger.logger.i(
                      'Book button tapped for slot: ${slot.startTime}',
                    );

                    // Get current pitch info
                    final pitchSettingsAsync = ref.watch(
                      pitchSettingsProvider(selectedPitchId),
                    );
                    final pitchName =
                        pitchSettingsAsync.asData?.value.name ??
                        'Unknown Pitch';
                    final currentSelectedPitchIdFromProvider = ref.read(
                      selectedPitchIdProvider,
                    );

                    if (currentSelectedPitchIdFromProvider == null) {
                      app_logger.logger.w(
                        '[SlotListItem] selectedPitchIdProvider is null before navigation.',
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Error: Pitch not selected. Please select a pitch first.',
                          ),
                        ),
                      );
                      return;
                    }

                    // Mark slot as optimistically booked
                    ref
                        .read(real_time.optimisticBookingStateProvider.notifier)
                        .markSlotAsOptimisticallyBooked(
                          slot.startTime,
                          slot.endTime,
                        );

                    try {
                      context.go(
                        '/booking-confirmation?'
                        'pitchId=${currentSelectedPitchIdFromProvider.toString()}&'
                        'pitchName=${Uri.encodeComponent(pitchName)}&'
                        'selectedDate=${selectedDate.toIso8601String()}&'
                        'slotStartTime=${slot.startTime.toIso8601String()}&'
                        'slotEndTime=${slot.endTime.toIso8601String()}',
                      );
                    } catch (e, s) {
                      // Clear optimistic booking on navigation error
                      ref
                          .read(
                            real_time.optimisticBookingStateProvider.notifier,
                          )
                          .clearOptimisticBooking(slot.startTime, slot.endTime);

                      app_logger.logger.e(
                        '[SlotListItem] Navigation to booking confirmation failed',
                        e,
                        s,
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Error navigating to booking confirmation.',
                          ),
                        ),
                      );
                    }
                  },
                  child: Text(
                    buttonText,
                    style: const TextStyle(inherit: false),
                  ),
                ),
              ),
            ),
          );
        }
      },
      loading: () {
        app_logger.logger.i(
          'SlotListItem build: userBookingsAsync is loading.',
        );
        return const Card(
          elevation: 2.0,
          margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
          child: ListTile(
            title: Text('Loading bookings...'),
            subtitle: Text('Please wait'),
            trailing: CircularProgressIndicator(),
          ),
        );
      },
      error: (error, stackTrace) {
        // Corrected logger call
        app_logger.logger.e(
          'SlotListItem build: Error loading user bookings.',
          error,
          stackTrace,
        );
        return Card(
          elevation: 2.0,
          margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
          child: ListTile(
            title: const Text('Error loading bookings'),
            subtitle: Text(error.toString()),
            trailing: const Icon(Icons.error, color: Colors.red),
          ),
        );
      },
    );
  }
}
