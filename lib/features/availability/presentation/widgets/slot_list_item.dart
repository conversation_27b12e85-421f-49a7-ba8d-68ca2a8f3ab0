import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:skillz/features/availability/domain/time_slot_info_model.dart';
import 'package:skillz/features/booking/data/booking_repository.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import 'package:skillz/features/availability/application/real_time_availability_provider.dart'
    as real_time;
import 'package:skillz/core/utils/logger_service.dart' as app_logger;
import 'package:skillz/features/booking/domain/booking_model.dart'
    as domain_booking;
import 'package:skillz/core/utils/date_formatters.dart';

class SlotListItem extends ConsumerWidget {
  final TimeSlotInfo slot;
  final DateTime selectedDate;
  final int selectedPitchId;

  const SlotListItem({
    super.key,
    required this.slot,
    required this.selectedDate,
    required this.selectedPitchId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userBookingsAsync = ref.watch(userBookingsProvider);
    final pitchSettingsAsync = ref.watch(
      pitchSettingsProvider(selectedPitchId),
    );
    final optimisticBookingState = ref.watch(
      real_time.optimisticBookingStateProvider,
    );

    app_logger.logger.i(
      'SlotListItem build: checking user bookings and pitch settings',
    );

    // Check if this slot is optimistically booked
    final slotKey =
        '${slot.startTime.toIso8601String()}_${slot.endTime.toIso8601String()}';
    final isOptimisticallyBooked = optimisticBookingState[slotKey] ?? false;

    // Handle already booked slots or optimistically booked slots
    if (slot.isBooked || isOptimisticallyBooked) {
      String subtitle = slot.isBooked ? 'Booked' : 'Booking...';
      Color cardColor = slot.isBooked ? Colors.red[50]! : Colors.orange[50]!;

      return Card(
        color: cardColor,
        elevation: 2.0,
        margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
        child: ListTile(
          title: Text(
            '${AppDateFormats.shortTime.format(slot.startTime.toLocal())} - ${AppDateFormats.shortTime.format(slot.endTime.toLocal())}',
            style: const TextStyle(color: Colors.grey),
          ),
          subtitle: Text(subtitle, style: const TextStyle(color: Colors.grey)),
          trailing: ElevatedButton(
            onPressed: null, // Disabled for booked slots
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[300],
              disabledForegroundColor: Colors.grey.withValues(alpha: 0.38),
              disabledBackgroundColor: Colors.grey.withValues(alpha: 0.12),
            ),
            child: Text(
              subtitle,
              style: const TextStyle(color: Colors.grey, inherit: false),
            ),
          ),
        ),
      );
    }

    // Check user bookings and pitch settings for booking limit
    return userBookingsAsync.when(
      data: (bookings) {
        return pitchSettingsAsync.when(
          data: (pitchSettings) {
            final upcomingBookingsCount =
                bookings
                    .where(
                      (booking) =>
                          booking.slotStartTime.isAfter(DateTime.now()) &&
                          booking.status !=
                              domain_booking.BookingStatus.cancelledByUser &&
                          booking.status !=
                              domain_booking.BookingStatus.cancelledByAdmin,
                    )
                    .length;

            final maxBookings = pitchSettings.maxBookingsPerUser;
            final bookingLimitReached = upcomingBookingsCount >= maxBookings;

            app_logger.logger.i(
              'SlotListItem: upcomingBookingsCount = $upcomingBookingsCount, maxBookings = $maxBookings, limitReached = $bookingLimitReached',
            );

            if (bookingLimitReached) {
              // Show booking limit reached UI
              return Card(
                elevation: 2.0,
                margin: const EdgeInsets.symmetric(
                  vertical: 8.0,
                  horizontal: 5.0,
                ),
                child: ListTile(
                  title: Text(
                    '${AppDateFormats.shortTime.format(slot.startTime.toLocal())} - ${AppDateFormats.shortTime.format(slot.endTime.toLocal())}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  subtitle: const Text(
                    'Unavailable',
                    style: TextStyle(color: Colors.grey),
                  ),
                  trailing: SizedBox(
                    width: 120,
                    child: ElevatedButton(
                      onPressed: () {
                        app_logger.logger.i(
                          'Limit Reached button tapped. Showing SnackBar.',
                        );
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'You have reached the maximum limit of $maxBookings active bookings.',
                            ),
                            action: SnackBarAction(
                              label: 'View Bookings',
                              onPressed: () {
                                app_logger.logger.i(
                                  'View Bookings SnackBar action tapped.',
                                );
                                context.go('/my_bookings');
                              },
                            ),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[300],
                        disabledForegroundColor: Colors.grey.withValues(
                          alpha: 0.38,
                        ),
                        disabledBackgroundColor: Colors.grey.withValues(
                          alpha: 0.12,
                        ),
                      ),
                      child: Text(
                        'Limit ($upcomingBookingsCount/$maxBookings)',
                        style: const TextStyle(
                          color: Colors.black87,
                          inherit: false,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }

            // Available slot - show book button
            return _buildBookButton(context, ref);
          },
          loading: () => _buildLoadingCard(),
          error: (error, stackTrace) {
            app_logger.logger.e(
              'Error loading pitch settings for booking limit check',
              error,
              stackTrace,
            );
            // If we can't load pitch settings, show book button (backend will enforce)
            return _buildBookButton(context, ref);
          },
        );
      },
      loading: () => _buildLoadingCard(),
      error: (error, stackTrace) {
        app_logger.logger.e(
          'Error loading user bookings for booking limit check',
          error,
          stackTrace,
        );
        // If we can't load user bookings, show book button (backend will enforce)
        return _buildBookButton(context, ref);
      },
    );
  }

  Widget _buildBookButton(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
      child: ListTile(
        title: Text(
          '${AppDateFormats.shortTime.format(slot.startTime.toLocal())} - ${AppDateFormats.shortTime.format(slot.endTime.toLocal())}',
          style: const TextStyle(color: Colors.black),
        ),
        trailing: SizedBox(
          width: 120,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              elevation: 2.0,
              shadowColor: Colors.black.withValues(alpha: 0.2),
              side: BorderSide(
                color: Theme.of(context).colorScheme.primaryContainer,
                width: 1,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              textStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                inherit: false,
              ),
            ),
            onPressed: () => _handleBookButtonTap(context, ref),
            child: const Text('Book', style: TextStyle(inherit: false)),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return const Card(
      elevation: 2.0,
      margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
      child: ListTile(
        title: Text('Loading...'),
        subtitle: Text('Please wait'),
        trailing: CircularProgressIndicator(),
      ),
    );
  }

  Future<void> _handleBookButtonTap(BuildContext context, WidgetRef ref) async {
    app_logger.logger.i('Book button tapped for slot: ${slot.startTime}');

    // Get current pitch info
    final pitchSettingsAsync = ref.watch(
      pitchSettingsProvider(selectedPitchId),
    );
    final pitchName = pitchSettingsAsync.asData?.value.name ?? 'Unknown Pitch';
    final currentSelectedPitchIdFromProvider = ref.read(
      selectedPitchIdProvider,
    );

    if (currentSelectedPitchIdFromProvider == null) {
      app_logger.logger.w(
        '[SlotListItem] selectedPitchIdProvider is null before navigation.',
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Error: Pitch not selected. Please select a pitch first.',
          ),
        ),
      );
      return;
    }

    // Mark slot as optimistically booked
    ref
        .read(real_time.optimisticBookingStateProvider.notifier)
        .markSlotAsOptimisticallyBooked(slot.startTime, slot.endTime);

    try {
      context.go(
        '/booking-confirmation?'
        'pitchId=${currentSelectedPitchIdFromProvider.toString()}&'
        'pitchName=${Uri.encodeComponent(pitchName)}&'
        'selectedDate=${selectedDate.toIso8601String()}&'
        'slotStartTime=${slot.startTime.toIso8601String()}&'
        'slotEndTime=${slot.endTime.toIso8601String()}',
      );
    } catch (e, s) {
      // Clear optimistic booking on navigation error
      ref
          .read(real_time.optimisticBookingStateProvider.notifier)
          .clearOptimisticBooking(slot.startTime, slot.endTime);

      app_logger.logger.e(
        '[SlotListItem] Navigation to booking confirmation failed',
        e,
        s,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Error navigating to booking confirmation.'),
        ),
      );
    }
  }
}
