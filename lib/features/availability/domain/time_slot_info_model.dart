import 'package:flutter/foundation.dart';

@immutable
class TimeSlotInfo {
  final DateTime startTime;
  final DateTime endTime;
  final bool isBooked;
  final String?
  bookingId; // Optional: if you want to store who booked it or some ID

  const TimeSlotInfo({
    required this.startTime,
    required this.endTime,
    required this.isBooked,
    this.bookingId,
  });

  /// Factory constructor for creating TimeSlotInfo from database JSON
  /// Maps database time_slots table structure to TimeSlotInfo model
  factory TimeSlotInfo.fromDatabaseJson(Map<String, dynamic> json) {
    // Parse date and time components from database
    final slotDate = DateTime.parse(json['slot_date'] as String);
    final startTimeStr = json['start_time'] as String;
    final endTimeStr = json['end_time'] as String;

    // Parse time strings (HH:mm:ss format)
    final startTimeParts = startTimeStr.split(':');
    final endTimeParts = endTimeStr.split(':');

    final startTime = DateTime(
      slotDate.year,
      slotDate.month,
      slotDate.day,
      int.parse(startTimeParts[0]),
      int.parse(startTimeParts[1]),
    );

    final endTime = DateTime(
      slotDate.year,
      slotDate.month,
      slotDate.day,
      int.parse(endTimeParts[0]),
      int.parse(endTimeParts[1]),
    );

    // Handle booking status - convert booking_id to string if not null
    final rawBookingId = json['booking_id'];
    final bookingId = rawBookingId?.toString();

    return TimeSlotInfo(
      startTime: startTime,
      endTime: endTime,
      isBooked: json['is_booked'] as bool? ?? false,
      bookingId: bookingId,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimeSlotInfo &&
          runtimeType == other.runtimeType &&
          startTime == other.startTime &&
          endTime == other.endTime &&
          isBooked == other.isBooked &&
          bookingId == other.bookingId;

  @override
  int get hashCode =>
      startTime.hashCode ^
      endTime.hashCode ^
      isBooked.hashCode ^
      bookingId.hashCode;

  TimeSlotInfo copyWith({
    DateTime? startTime,
    DateTime? endTime,
    bool? isBooked,
    String? bookingId,
  }) {
    return TimeSlotInfo(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isBooked: isBooked ?? this.isBooked,
      bookingId: bookingId ?? this.bookingId,
    );
  }

  /// Check if this slot is optimistically booked (temporary booking state)
  bool get isOptimisticallyBooked => bookingId == 'optimistic';

  /// Check if this slot is in the past (unavailable due to time)
  bool get isPastSlot => bookingId == 'past_slot';

  /// Check if this slot is actually booked (confirmed booking)
  bool get isConfirmedBooked =>
      isBooked && !isOptimisticallyBooked && !isPastSlot;

  /// Get display status for UI
  String get displayStatus {
    if (isPastSlot) return 'Past';
    if (isOptimisticallyBooked) return 'Booking...';
    if (isConfirmedBooked) return 'Booked';
    return 'Available';
  }

  /// Check if this slot can be booked
  bool get canBeBooked => !isBooked && !isPastSlot;

  @override
  String toString() {
    return 'TimeSlotInfo(startTime: $startTime, endTime: $endTime, isBooked: $isBooked, bookingId: $bookingId, status: $displayStatus)';
  }
}
