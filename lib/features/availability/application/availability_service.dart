import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart'; // Required for DateTimeRange
// Updated import
import 'package:mockito/annotations.dart'; // Import mockito
import 'package:skillz/core/utils/logger_service.dart'; // Added logger import
import 'package:skillz/core/utils/date_time_extensions.dart'; // Import DateTime extensions
import 'package:skillz/features/auth/application/auth_service.dart'; // Added import for supabaseClientProvider
import '../domain/pitch_settings_model.dart';
import 'package:skillz/features/booking/domain/booking_model.dart'; // Use main booking model
import '../domain/time_slot_info_model.dart';

// Add this annotation to generate a mock class for AvailabilityService
@GenerateMocks([AvailabilityService])
class AvailabilityService {
  final SupabaseClient _supabaseClient;

  AvailabilityService(this._supabaseClient);

  Future<PitchSettings> getPitchSettings(int pitchId) async {
    try {
      final response =
          await _supabaseClient
              .from('pitch_settings')
              .select()
              .eq('id', pitchId)
              .single();
      return PitchSettings.fromJson(response);
    } catch (e, stackTrace) {
      // Added stackTrace
      logger.e(
        'Error fetching pitch settings for pitch $pitchId',
        e,
        stackTrace,
      ); // Replaced print with logger.e
      rethrow;
    }
  }

  Future<List<PitchSettings>> getAllPitchSettings() async {
    try {
      final response = await _supabaseClient.from('pitch_settings').select();
      logger.d(
        'Raw response from Supabase for all pitch settings: $response',
      ); // Kept one debug log for now

      final responseList = response as List;

      return responseList.map((data) {
        try {
          return PitchSettings.fromJson(data as Map<String, dynamic>);
        } catch (e, stackTrace) {
          // Added stackTrace
          logger.e(
            'Error parsing individual pitch data: $data',
            e,
            stackTrace,
          ); // Replaced print with logger.e
          rethrow;
        }
      }).toList();
    } catch (e, stackTrace) {
      // Added stackTrace
      logger.e(
        'Error fetching all pitch settings',
        e,
        stackTrace,
      ); // Replaced print with logger.e
      rethrow;
    }
  }

  Future<List<Booking>> getBookingsForDate(DateTime date, int pitchId) async {
    try {
      final DateTime startDate = date.startOfDay; // Used extension
      final DateTime endDate = date.endOfDay; // Used extension

      // 🔍 DEBUG: Add detailed logging to understand timezone issues
      logger.d('🔍 getBookingsForDate DEBUG:');
      logger.d('  📅 Input date: $date');
      logger.d('  📅 Input date (local): ${date.toLocal()}');
      logger.d('  📅 Input date (UTC): ${date.toUtc()}');
      logger.d('  📅 Start date: $startDate');
      logger.d('  📅 Start date (local): ${startDate.toLocal()}');
      logger.d('  📅 Start date (UTC): ${startDate.toUtc()}');
      logger.d('  📅 Start date ISO: ${startDate.toIso8601String()}');
      logger.d('  📅 End date: $endDate');
      logger.d('  📅 End date (local): ${endDate.toLocal()}');
      logger.d('  📅 End date (UTC): ${endDate.toUtc()}');
      logger.d('  📅 End date ISO: ${endDate.toIso8601String()}');

      final response = await _supabaseClient
          .from('bookings')
          .select()
          .eq('pitch_id', pitchId) // Added pitch_id filter
          .gte('slot_start_time', startDate.toIso8601String())
          .lte('slot_start_time', endDate.toIso8601String())
          .eq(
            'status',
            BookingStatus.confirmed.toJson(),
          ) // Use enum to ensure correctness
          .order('slot_start_time', ascending: true);

      logger.d('🔍 Database query results:');
      logger.d('  📊 Raw response: $response');

      final bookings =
          (response as List)
              .map(
                (bookingData) =>
                    Booking.fromJson(bookingData as Map<String, dynamic>),
              )
              .toList();

      // 🔍 DEBUG: Log each booking with timezone info
      for (int i = 0; i < bookings.length; i++) {
        final booking = bookings[i];
        logger.d('  📋 Booking $i:');
        logger.d('    🕐 Start: ${booking.slotStartTime}');
        logger.d('    🕐 Start (local): ${booking.slotStartTime.toLocal()}');
        logger.d('    🕐 Start (UTC): ${booking.slotStartTime.toUtc()}');
        logger.d('    🕐 End: ${booking.slotEndTime}');
        logger.d('    🕐 End (local): ${booking.slotEndTime.toLocal()}');
        logger.d('    🕐 End (UTC): ${booking.slotEndTime.toUtc()}');
        logger.d('    📊 Status: ${booking.status.name}');
      }

      return bookings;
    } catch (e, stackTrace) {
      // Added stackTrace
      logger.e(
        'Error fetching bookings for date $date and pitch $pitchId',
        e,
        stackTrace,
      ); // Replaced print with logger.e and added pitchId
      rethrow;
    }
  }
}

final availabilityServiceProvider = Provider<AvailabilityService>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider);
  return AvailabilityService(supabaseClient);
});

// Provider to fetch all pitch settings (e.g., for a dropdown)
final allPitchesProvider = FutureProvider<List<PitchSettings>>((ref) async {
  // New provider
  final availabilityService = ref.watch(availabilityServiceProvider);
  return availabilityService.getAllPitchSettings();
});

// Updated to be a family provider that takes pitchId
final pitchSettingsProvider = FutureProvider.family<PitchSettings, int>((
  ref,
  pitchId,
) async {
  // Changed to family
  final availabilityService = ref.watch(availabilityServiceProvider);
  return availabilityService.getPitchSettings(pitchId); // Pass pitchId
});

// Define a record type for the bookingsForDateProvider family parameter
typedef BookingsForDateParams =
    ({DateTime date, int pitchId}); // New record type

// Updated to be a family provider that takes BookingsForDateParams
final bookingsForDateProvider =
    FutureProvider.family<List<Booking>, BookingsForDateParams>((
      ref,
      params, // Changed parameter to use the record type
    ) async {
      final availabilityService = ref.watch(availabilityServiceProvider);
      return availabilityService.getBookingsForDate(
        params.date,
        params.pitchId,
      ); // Pass pitchId
    });

// Helper function to parse time string (e.g., "HH:mm:ss") to TimeOfDay
TimeOfDay _parseTimeOfDay(String timeString) {
  // final format = DateFormat("HH:mm:ss"); // Removed
  // It's generally safer to parse time without a specific format if it's just HH:mm:ss
  // or ensure the input string strictly matches. For now, let's assume it's valid.
  // Consider adding a new format to AppDateFormats if "HH:mm:ss" is standard.
  // For simplicity, if AppDateFormats.hourMinute can parse it (if seconds are 00),
  // or if you add a specific AppDateFormats.hourMinuteSecond, that would be better.
  // Temporarily, we can keep the direct parsing but ideally, this should also be standardized.
  final parts = timeString.split(':');
  return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  // If seconds are crucial and present:
  // return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]), second: int.parse(parts[2]));
}

// Define a record type for the family parameter
typedef AvailableSlotsParams = ({DateTime date, int pitchId});

// Provider to hold the currently selected pitch ID
final selectedPitchIdProvider = StateProvider<int?>(
  (ref) => null,
); // New provider

final availableSlotsProvider = FutureProvider.family<
  List<TimeSlotInfo>,
  AvailableSlotsParams
>((ref, params) async {
  logger.d(
    '📍 Static availableSlotsProvider called for date: ${params.date}, pitch: ${params.pitchId}',
  );

  // Watch the pitchSettingsProvider with the specific pitchId from params
  final pitchSettings = await ref.watch(
    pitchSettingsProvider(params.pitchId).future,
  );
  // Watch bookingsForDateProvider with both date and pitchId from params
  final bookings = await ref.watch(bookingsForDateProvider(params).future);

  logger.d('📍 Static provider: Got ${bookings.length} bookings from database');
  for (int i = 0; i < bookings.length; i++) {
    final booking = bookings[i];
    logger.d(
      '  📍 Static Booking $i: ${booking.slotStartTime.toLocal()} - ${booking.slotEndTime.toLocal()} (Status: ${booking.status.name})',
    );
  }

  final List<TimeSlotInfo> allTimeSlots = [];

  final TimeOfDay openTimeOfDay = _parseTimeOfDay(pitchSettings.openTime);
  final TimeOfDay closeTimeOfDay = _parseTimeOfDay(pitchSettings.closeTime);

  final openDateTime = DateTime(
    params.date.year,
    params.date.month,
    params.date.day,
    openTimeOfDay.hour,
    openTimeOfDay.minute,
  );

  final closeDateTime = DateTime(
    params.date.year,
    params.date.month,
    params.date.day,
    closeTimeOfDay.hour,
    closeTimeOfDay.minute,
  );

  // Consider using params.date.startOfDay and then adding TimeOfDay if it simplifies
  // final openDateTime = params.date.startOfDay.add(Duration(hours: openTimeOfDay.hour, minutes: openTimeOfDay.minute));
  // final closeDateTime = params.date.startOfDay.add(Duration(hours: closeTimeOfDay.hour, minutes: closeTimeOfDay.minute));

  final slotDuration = Duration(minutes: pitchSettings.slotDurationMinutes);

  DateTime currentSlotStart = openDateTime;

  while (currentSlotStart.isBefore(closeDateTime)) {
    final currentSlotEnd = currentSlotStart.add(slotDuration);
    if (currentSlotEnd.isAfter(closeDateTime)) {
      break;
    }

    final potentialSlotRange = DateTimeRange(
      start: currentSlotStart,
      end: currentSlotEnd,
    );

    Booking? existingBooking; // Declare as nullable Booking
    try {
      existingBooking = bookings.firstWhere((booking) {
        final bookingSlotRange = DateTimeRange(
          start: booking.slotStartTime.toLocal(),
          end: booking.slotEndTime.toLocal(),
        );
        return potentialSlotRange.start.isBefore(bookingSlotRange.end) &&
            potentialSlotRange.end.isAfter(bookingSlotRange.start);
      });
    } catch (e) {
      // firstWhere throws StateError if no element is found and orElse is not provided.
      // We catch it and existingBooking remains null, which is the desired outcome.
      existingBooking = null;
    }

    final bool isBooked = existingBooking != null;

    allTimeSlots.add(
      TimeSlotInfo(
        startTime: currentSlotStart,
        endTime: currentSlotEnd,
        isBooked: isBooked,
        bookingId: isBooked ? existingBooking.id.toString() : null,
      ),
    );

    currentSlotStart = currentSlotEnd;
  }
  logger.d(
    // Replaced print with logger.d
    'Generated ${allTimeSlots.length} total slots for ${params.date} (${allTimeSlots.where((s) => s.isBooked).length} booked, ${allTimeSlots.where((s) => !s.isBooked).length} available) for pitch ${params.pitchId}',
  );
  return allTimeSlots.where((slot) => !slot.isBooked).toList();
});
