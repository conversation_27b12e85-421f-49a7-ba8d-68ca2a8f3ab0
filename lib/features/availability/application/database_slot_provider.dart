// Database Slot Provider - TDD Implementation
// This class replaces all legacy client-side slot calculation logic
// with database-centric slot management to fix the 3-slot offset bug

import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:skillz/features/availability/domain/time_slot_info_model.dart';

/// Exception thrown when database slot operations fail
class DatabaseSlotException implements Exception {
  final String message;
  final dynamic originalError;

  const DatabaseSlotException(this.message, [this.originalError]);

  @override
  String toString() => 'DatabaseSlotException: $message';
}

/// Provider that fetches slot data from Supabase database
/// Replaces all legacy slot calculation logic
class DatabaseSlotProvider {
  final SupabaseClient supabaseClient;

  DatabaseSlotProvider({required this.supabaseClient});

  /// Fetch slots for a specific pitch and date from database
  /// This is the main method that replaces legacy slot calculation
  Future<List<TimeSlotInfo>> fetchSlots(int pitchId, DateTime date) async {
    try {
      // Format date for database query (YYYY-MM-DD format)
      final dateString = date.toIso8601String().split('T')[0];

      // Query time_slots table with proper filtering and ordering
      final response = await supabaseClient
          .from('time_slots')
          .select()
          .eq('pitch_id', pitchId)
          .eq('slot_date', dateString)
          .order('start_time');

      // Convert database response to TimeSlotInfo objects
      final allSlots =
          (response as List<dynamic>)
              .map(
                (json) =>
                    TimeSlotInfo.fromDatabaseJson(json as Map<String, dynamic>),
              )
              .toList();

      // Filter out past slots for today, show all slots for future dates
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final selectedDate = DateTime(date.year, date.month, date.day);

      List<TimeSlotInfo> filteredSlots;
      if (selectedDate.isAtSameMomentAs(today)) {
        // For today, filter out slots that have already started
        filteredSlots =
            allSlots.where((slot) {
              return slot.startTime.isAfter(now);
            }).toList();
      } else {
        // For future dates, show all slots
        filteredSlots = allSlots;
      }

      // Sort slots by start time to ensure proper ordering
      filteredSlots.sort((a, b) => a.startTime.compareTo(b.startTime));

      return filteredSlots;
    } catch (e) {
      throw DatabaseSlotException(
        'Failed to fetch slots for pitch $pitchId on $date',
        e,
      );
    }
  }

  /// Fetch slots with automatic generation if none exist
  /// Handles the case where slots need to be created for future dates
  Future<List<TimeSlotInfo>> fetchSlotsWithGeneration(
    int pitchId,
    DateTime date,
  ) async {
    try {
      // First, try to fetch existing slots (with filtering and sorting)
      final existingSlots = await fetchSlots(pitchId, date);

      // If slots exist, return them
      if (existingSlots.isNotEmpty) {
        return existingSlots;
      }

      // If no slots exist, generate them using database function
      final dateString = date.toIso8601String().split('T')[0];
      await supabaseClient.rpc(
        'generate_time_slots',
        params: {'target_pitch_id': pitchId, 'target_date': dateString},
      );

      // Fetch the newly generated slots (with filtering and sorting)
      return await fetchSlots(pitchId, date);
    } catch (e) {
      throw DatabaseSlotException(
        'Failed to fetch/generate slots for pitch $pitchId on $date',
        e,
      );
    }
  }

  /// Watch for real-time slot changes
  /// Returns a stream of slot updates for reactive UI
  Stream<List<TimeSlotInfo>> watchSlots(int pitchId, DateTime date) {
    // Create a stream controller to manage the slot updates
    late StreamController<List<TimeSlotInfo>> controller;
    late RealtimeChannel channel;

    controller = StreamController<List<TimeSlotInfo>>.broadcast(
      onListen: () async {
        try {
          // Fetch initial slots
          final initialSlots = await fetchSlots(pitchId, date);
          controller.add(initialSlots);

          // Set up real-time subscription
          channel = supabaseClient
              .channel('time_slots_changes')
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'time_slots',
                filter: PostgresChangeFilter(
                  type: PostgresChangeFilterType.eq,
                  column: 'pitch_id',
                  value: pitchId,
                ),
                callback: (payload) async {
                  try {
                    // Refetch slots when changes occur
                    final updatedSlots = await fetchSlots(pitchId, date);
                    controller.add(updatedSlots);
                  } catch (e) {
                    controller.addError(
                      DatabaseSlotException(
                        'Failed to fetch updated slots during real-time update',
                        e,
                      ),
                    );
                  }
                },
              );

          channel.subscribe();
        } catch (e) {
          controller.addError(
            DatabaseSlotException(
              'Failed to initialize real-time slot watching',
              e,
            ),
          );
        }
      },
      onCancel: () {
        channel.unsubscribe();
      },
    );

    return controller.stream;
  }

  /// Generate slots for a specific pitch and date
  /// Calls the database function to create time slots
  Future<Map<String, dynamic>> generateSlots(int pitchId, DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      final result = await supabaseClient.rpc(
        'generate_time_slots',
        params: {'target_pitch_id': pitchId, 'target_date': dateString},
      );

      return result as Map<String, dynamic>;
    } catch (e) {
      throw DatabaseSlotException(
        'Failed to generate slots for pitch $pitchId on $date',
        e,
      );
    }
  }
}
