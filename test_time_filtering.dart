#!/usr/bin/env dart
// Simple test script to verify time slot filtering and ordering logic

void main() {
  print('🧪 Testing Time Slot Filtering and Ordering Logic');
  print('=' * 60);

  // Simulate current time
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);

  print('Current time: $now');
  print('Today (start of day): $today');
  print('');

  // Test case 1: Today's slots - should filter past ones
  print('Test 1: Today\'s slots');
  print('-' * 30);

  final todaySlots = generateTestSlots(today);
  print('Generated slots for today:');
  for (var slot in todaySlots) {
    print('  ${slot.startTime} - ${slot.endTime}');
  }

  final filteredTodaySlots = filterAndSortSlots(todaySlots, today, now);
  print('');
  print('Filtered slots (past slots removed):');
  for (var slot in filteredTodaySlots) {
    print('  ${slot.startTime} - ${slot.endTime}');
  }

  print('');

  // Test case 2: Future date - should show all slots
  print('Test 2: Future date slots');
  print('-' * 30);

  final futureDate = today.add(Duration(days: 3));
  final futureSlots = generateTestSlots(futureDate);
  print('Generated slots for future date ($futureDate):');
  for (var slot in futureSlots) {
    print('  ${slot.startTime} - ${slot.endTime}');
  }

  final filteredFutureSlots = filterAndSortSlots(futureSlots, futureDate, now);
  print('');
  print('Filtered slots (should include all):');
  for (var slot in filteredFutureSlots) {
    print('  ${slot.startTime} - ${slot.endTime}');
  }

  print('');
  print('✅ Time filtering and ordering logic works correctly!');
}

class TimeSlot {
  final DateTime startTime;
  final DateTime endTime;
  final bool isBooked;

  TimeSlot({
    required this.startTime,
    required this.endTime,
    this.isBooked = false,
  });
}

List<TimeSlot> generateTestSlots(DateTime date) {
  // Generate slots from 9 AM to 5 PM (1-hour slots)
  final slots = <TimeSlot>[];
  for (int hour = 9; hour <= 16; hour++) {
    final startTime = DateTime(date.year, date.month, date.day, hour);
    final endTime = startTime.add(Duration(hours: 1));
    slots.add(TimeSlot(startTime: startTime, endTime: endTime));
  }

  // Shuffle to simulate unsorted database results
  slots.shuffle();
  return slots;
}

List<TimeSlot> filterAndSortSlots(
  List<TimeSlot> allSlots,
  DateTime selectedDate,
  DateTime now,
) {
  final today = DateTime(now.year, now.month, now.day);
  final selectedDateStart = DateTime(
    selectedDate.year,
    selectedDate.month,
    selectedDate.day,
  );

  List<TimeSlot> filteredSlots;

  if (selectedDateStart.isAtSameMomentAs(today)) {
    // For today, filter out slots that have already started
    filteredSlots =
        allSlots.where((slot) {
          return slot.startTime.isAfter(now);
        }).toList();
  } else {
    // For future dates, show all slots
    filteredSlots = allSlots;
  }

  // Sort slots by start time to ensure proper ordering
  filteredSlots.sort((a, b) => a.startTime.compareTo(b.startTime));

  return filteredSlots;
}
