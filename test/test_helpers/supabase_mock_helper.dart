// This file provides mock implementations of Supabase dependencies for tests

import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:async';
import 'package:mock_supabase_http_client/mock_supabase_http_client.dart';

// Extension to help check if Supabase global instance seems initialized
extension SupabaseUninitializedCheck on Supabase {
  static SupabaseClient? get instanceIfInitialized {
    try {
      return Supabase.instance.client;
    } catch (e) {
      // Typically throws if not initialized
      if (e.toString().contains("Instance needs to be initialized") ||
          e.toString().contains("has not been initialized")) {
        return null;
      }
      rethrow;
    }
  }
}

// Mock classes
class MockGoTrueClient extends Mock implements GoTrueClient {}

class MockAuthResponse extends Mock implements AuthResponse {}

class MockUser extends Mock implements User {}

class MockSession extends Mock implements Session {}
// No need to mock SupabaseClient itself if we are constructing it with other mocks.

// Global instances of core mocks, managed by MockSupabaseManager
final MockGoTrueClient _mockGoTrueClient = MockGoTrueClient();
final MockSupabaseHttpClient _mockSupabaseHttpClient = MockSupabaseHttpClient();

StreamController<AuthState>? _authStateController;

class MockSupabaseManager {
  SupabaseClient? _constructedMockClient;

  SupabaseClient get client {
    if (_constructedMockClient == null) {
      throw StateError(
        'MockSupabaseManager not initialized or client not constructed. Call initialize() first.',
      );
    }
    return _constructedMockClient!;
  }

  MockGoTrueClient get rawMockAuthClient => _mockGoTrueClient;
  MockSupabaseHttpClient get rawMockHttpClient => _mockSupabaseHttpClient;

  Future<void> initialize({bool initialUserLoggedIn = true}) async {
    reset(_mockGoTrueClient);
    // _mockSupabaseHttpClient.reset(); // mock_supabase_http_client manages handlers per test via its own API

    _authStateController?.close();
    _authStateController = StreamController<AuthState>.broadcast();

    final mockUser = MockUser();
    when(() => mockUser.id).thenReturn('test-user-id');
    when(() => mockUser.email).thenReturn('<EMAIL>');
    when(() => mockUser.appMetadata).thenReturn({});
    when(() => mockUser.userMetadata).thenReturn({});
    when(() => mockUser.aud).thenReturn('authenticated');
    when(() => mockUser.createdAt).thenReturn(DateTime.now().toIso8601String());

    final mockSession = MockSession();
    when(() => mockSession.user).thenReturn(mockUser);
    when(() => mockSession.accessToken).thenReturn('mock-access-token');
    when(() => mockSession.refreshToken).thenReturn('mock-refresh-token');
    when(() => mockSession.expiresIn).thenReturn(3600);
    when(() => mockSession.tokenType).thenReturn('bearer');

    final signedInAuthState = AuthState(AuthChangeEvent.signedIn, mockSession);
    final signedOutAuthState = AuthState(AuthChangeEvent.signedOut, null);
    final AuthState resolvedInitialAuthState = AuthState(
      AuthChangeEvent.initialSession,
      initialUserLoggedIn ? mockSession : null,
    );

    // Setup GoTrueClient mocks
    if (initialUserLoggedIn) {
      when(() => _mockGoTrueClient.currentSession).thenReturn(mockSession);
      when(() => _mockGoTrueClient.currentUser).thenReturn(mockUser);
    } else {
      when(() => _mockGoTrueClient.currentSession).thenReturn(null);
      when(() => _mockGoTrueClient.currentUser).thenReturn(null);
    }

    when(
      () => _mockGoTrueClient.onAuthStateChange,
    ).thenAnswer((_) => _authStateController!.stream);

    // Note: initialSession property doesn't exist in this version of Supabase
    // so we skip mocking it. The initial auth state is handled via onAuthStateChange stream

    when(
      () => _mockGoTrueClient.signInWithPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenAnswer((invocation) async {
      when(() => _mockGoTrueClient.currentSession).thenReturn(mockSession);
      when(() => _mockGoTrueClient.currentUser).thenReturn(mockUser);
      _authStateController!.add(signedInAuthState);
      return AuthResponse(session: mockSession, user: mockUser);
    });

    when(
      () => _mockGoTrueClient.signUp(
        email: any(named: 'email'),
        password: any(named: 'password'),
        data: any(named: 'data'),
      ),
    ).thenAnswer(
      (_) async => AuthResponse(session: mockSession, user: mockUser),
    );

    when(
      () => _mockGoTrueClient.signUp(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenAnswer(
      (_) async => AuthResponse(session: mockSession, user: mockUser),
    );

    when(() => _mockGoTrueClient.signOut()).thenAnswer((_) async {
      when(() => _mockGoTrueClient.currentSession).thenReturn(null);
      when(() => _mockGoTrueClient.currentUser).thenReturn(null);
      _authStateController!.add(signedOutAuthState);
      // signOut in GoTrueClient is Future<void>
    });

    // Construct a SupabaseClient instance using our mocks.
    // This client is intended to be injected via providers in tests.
    _constructedMockClient = SupabaseClient(
      'https://mock.supabase.url',
      'mockAnonKey',
      httpClient: _mockSupabaseHttpClient,
      authOptions:
          const AuthClientOptions(), // Use default or specific options if needed
      // The GoTrueClient used by this _constructedMockClient will be a real one,
      // but configured with the mock URL/Key and potentially the httpClient.
      // Auth calls in tests should be directed to `manager.auth` (the _managedMockGoTrueClient).
    );

    // THIS IS THE CRITICAL PART:
    // We need to ensure that the .auth getter of our _constructedMockClient
    // returns our _managedMockGoTrueClient.
    // We can achieve this by creating a wrapper or by using a mocking framework's ability
    // to mock getters if SupabaseClient were an interface or mockable directly.
    // Since SupabaseClient is a concrete class, this is tricky.

    // Alternative: Mock Supabase itself.
    // class MockSupabase extends Mock implements Supabase {
    //   @override
    //   SupabaseClient get client => _constructedMockClient; // where _constructedMockClient.auth is also handled
    //   @override
    //   GoTrueClient get auth => _managedMockGoTrueClient;
    // }
    // This seems more robust. The MockSupabaseManager would then set up this MockSupabase.

    // For now, let's assume the tests will use mockSupabaseManager.auth for auth calls
    // and mockSupabaseManager.client for other calls (like .rpc, .from).
    // The .auth on mockSupabaseManager.client will be a real one, but hopefully unused if tests use mockSupabaseManager.auth.

    // Let's refine the constructor again, knowing we can't inject the auth client.
    // The SupabaseClient will initialize its own GoTrueClient.
    // We will primarily use _managedMockGoTrueClient for auth testing,
    // and _constructedMockClient (with its mocked httpClient) for database testing.

    // The issue might be that SupabaseClient's own GoTrueClient needs the same dummy URL and key.
    // And we need to ensure that when Supabase.instance.auth is called, it's our mock.

    // Let's look at the Supabase.initialize method:
    //   static Future<Supabase> initialize({
    //    required String supabaseUrl,
    //    required String supabaseKey,
    //    http.Client? httpClient,
    //    AuthClientOptions authOptions = const AuthClientOptions(),
    //    RealtimeClientOptions realtimeClientOptions = const RealtimeClientOptions(),
    //    StorageClientOptions storageOptions = const StorageClientOptions(),
    //    FunctionsClientOptions functionsOptions = const FunctionsClientOptions(),
    //  }) {
    //    // ...
    //    _instance = Supabase._(client); // client is new SupabaseClient(...)
    //    return _instance!;
    //  }
    // And Supabase.instance.auth just returns _instance!._client.auth.

    // So, if we provide a SupabaseClient that has a mocked httpClient, its .auth will be a real GoTrueClient
    // but constructed with the same dummy URL/key and potentially our httpClient if GoTrueClient uses it.
    // GoTrueClient constructor: GoTrueClient({required String url, required String apiKey, Map<String, String>? headers, http.Client? httpClient})
    // Yes, it can take an httpClient.

    // This means if we pass _mockSupabaseHttpClient to SupabaseClient's constructor,
    // its internally created GoTrueClient will also use _mockSupabaseHttpClient.
    // This could be useful if auth calls also go through the general httpClient, but they might use their own specific paths.

    // The most straightforward path is:
    // 1. MockGoTrueClient for all auth logic.
    // 2. MockSupabaseHttpClient for all database logic (select, insert, rpc).
    // 3. Construct a SupabaseClient with the mocked http client.
    // 4. In tests, provide the MockGoTrueClient where auth is needed, and the constructed SupabaseClient where the client is needed.

    // The error "The named parameter 'auth' isn't defined" means the SupabaseClient constructor
    // does not take 'auth:'. It might take 'authOptions:' or nothing directly for the GoTrueClient instance.
    // Let's check the SupabaseClient constructor signature carefully from the source or docs if possible.
    // From `supabase-dart/lib/src/supabase_client.dart`:
    // SupabaseClient(
    //   String supabaseUrl,
    //   String supabaseKey, {
    //   String schema = 'public',
    //   String? restUrl,
    //   String? realtimeUrl,
    //   String? authUrl, // This is new!
    //   http.Client? httpClient,
    //   Map<String, String>? headers,
    //   AuthClientOptions? authOptions, // This is what we should use for configuring auth, not injecting.
    //   StorageClientOptions? storageOptions,
    //   PostgrestClientOptions? postgrestClientOptions, // For configuring PostgrestClient
    //   FunctionsClientOptions? functionsClientOptions,
    //   RealtimeClientOptions? realtimeClientOptions,
    // })

    // We cannot pass an instance of GoTrueClient.
    // So, the _constructedMockClient will have its own GoTrueClient.
    // Our _managedMockGoTrueClient is separate.
    // This helper's purpose is to provide BOTH:
    // - a SupabaseClient configured with a mock HTTP client (for .from(), .rpc())
    // - a mock GoTrueClient (for .auth related calls)

    // The previous code for _getMockedSupabaseClient was:
    // SupabaseClient(
    //   'https://mock.supabase.url',
    //   'mockKey',
    //   httpClient: mockSupabaseHttpClient, // This is correct for db
    //   authOptions: const AuthClientOptions(), // Default options
    // );
    // This is fine for the client part. The auth part is handled by the separate mockGoTrueClient.

    // The error was in the _MockSupabaseManager constructor:
    // _constructedMockClient = SupabaseClient(
    //   'https://mock.supabase.url',
    //   'mockAnonKey',
    //   auth: _mockGoTrueClient, // <--- THIS IS THE ERROR
    //   httpClient: _mockSupabaseHttpClient,
    // );

    // It should be:
    _constructedMockClient = SupabaseClient(
      'https://mock.supabase.url',
      'mockAnonKey',
      httpClient: _mockSupabaseHttpClient,
      authOptions:
          const AuthClientOptions(), // Use default or specific options if needed
      // The GoTrueClient used by this _constructedMockClient will be a real one,
      // but configured with the mock URL/Key and potentially the httpClient.
      // Auth calls in tests should be directed to `manager.auth` (the _managedMockGoTrueClient).
    );

    // Now, let's ensure the manager provides both distinctly:
    // SupabaseClient get client => _constructedMockClient;
    // GoTrueClient get auth => _managedMockGoTrueClient;

    // This structure seems correct. The error was purely the constructor call.

    // MockSupabaseManager initialized successfully
    // (Removed verbose debug output to clean up test logs)

    // After constructing the client, ensure the initial auth state is emitted
    // so that any listeners on the _constructedMockClient.auth.onAuthStateChange pick it up.
    if (!_authStateController!.isClosed) {
      _authStateController!.add(resolvedInitialAuthState);
    }
  }

  void dispose() {
    _authStateController?.close();
    _authStateController = null;
    // _mockSupabaseHttpClient.dispose(); // If it had a dispose method
  }
}

// Singleton instance of the manager
final mockSupabaseManager = MockSupabaseManager();

// Call in test setUp or setUpAll
Future<void> setupSupabaseMocks({bool initialUserLoggedIn = true}) async {
  TestWidgetsFlutterBinding.ensureInitialized();
  // Initialize the manager, which sets up mocks and constructs a mock SupabaseClient
  await mockSupabaseManager.initialize(
    initialUserLoggedIn: initialUserLoggedIn,
  );
}

// Call in test tearDown or tearDownAll
void tearDownSupabaseMocks() {
  mockSupabaseManager.dispose();
}

// Convenience getters for direct access to the raw mocks if needed by tests
MockGoTrueClient get mockAuthClient => mockSupabaseManager.rawMockAuthClient;
MockSupabaseHttpClient get mockDbClient =>
    mockSupabaseManager.rawMockHttpClient;

// Helper to simulate auth state changes for testing widgets that react to them
void simulateAuthStateChange(AuthState state) {
  if (_authStateController != null && !_authStateController!.isClosed) {
    _authStateController!.add(state);
  } else {
    debugPrint(
      "Warning: AuthStateController not available or closed in mockSupabaseManager. Cannot simulate auth state change.",
    );
  }
}
