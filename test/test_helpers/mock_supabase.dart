import 'package:mocktail/mocktail.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Base Mocks
class MockSupabaseClient extends Mock implements SupabaseClient {}

class MockGoTrueClient extends Mock implements GoTrueClient {}

class MockPostgrestClient extends Mock implements PostgrestClient {}

class MockRealtimeClient extends Mock implements RealtimeClient {}

class MockFunctionsClient extends Mock implements FunctionsClient {}

class MockSupabaseStorageClient extends Mock implements SupabaseStorageClient {}

// Builder Mocks
// SupabaseQueryBuilder is not generic.
class MockSupabaseQueryBuilder extends Mock implements SupabaseQueryBuilder {}

class MockPostgrestTransformBuilder<T> extends Mock
    implements PostgrestTransformBuilder<T> {}

class MockPostgrestFilterBuilder<T> extends Mock
    implements PostgrestFilterBuilder<T> {}

class MockPostgrestBuilder extends Mock implements PostgrestBuilder {}

// Helper function to setup a basic mock SupabaseClient
MockSupabaseClient setupMockSupabaseClient({
  MockGoTrueClient? mockGoTrueClient,
  MockPostgrestClient? mockPostgrestClient,
  MockRealtimeClient? mockRealtimeClient,
  MockFunctionsClient? mockFunctionsClient,
  MockSupabaseStorageClient? mockStorageClient,
}) {
  final client = MockSupabaseClient();
  final auth = mockGoTrueClient ?? MockGoTrueClient();
  final realtime = mockRealtimeClient ?? MockRealtimeClient();
  final functions = mockFunctionsClient ?? MockFunctionsClient();
  final storage = mockStorageClient ?? MockSupabaseStorageClient();

  when(() => client.auth).thenReturn(auth);
  when(() => client.realtime).thenReturn(realtime);
  when(() => client.functions).thenReturn(functions);
  when(() => client.storage).thenReturn(storage);

  // Use non-generic MockSupabaseQueryBuilder and create mock filter builders
  final mockSupabaseQueryBuilder = MockSupabaseQueryBuilder();
  final mockFilterBuilder =
      MockPostgrestFilterBuilder<List<Map<String, dynamic>>>();
  final mockSingleFilterBuilder =
      MockPostgrestFilterBuilder<Map<String, dynamic>>();

  when(() => client.from(any())).thenReturn(mockSupabaseQueryBuilder);

  // Setup the mock chain for query methods
  // select() returns a PostgrestFilterBuilder
  when(
    () => mockSupabaseQueryBuilder.select(any()),
  ).thenReturn(mockFilterBuilder);
  when(() => mockSupabaseQueryBuilder.select()).thenReturn(mockFilterBuilder);

  // insert() returns a PostgrestFilterBuilder
  when(
    () => mockSupabaseQueryBuilder.insert(any()),
  ).thenReturn(mockFilterBuilder);

  // update() returns a PostgrestFilterBuilder
  when(
    () => mockSupabaseQueryBuilder.update(any()),
  ).thenReturn(mockFilterBuilder);

  // delete() returns a PostgrestFilterBuilder
  when(() => mockSupabaseQueryBuilder.delete()).thenReturn(mockFilterBuilder);

  // Filter methods return the same filter builder (for chaining)
  when(() => mockFilterBuilder.eq(any(), any())).thenReturn(mockFilterBuilder);
  when(() => mockFilterBuilder.gte(any(), any())).thenReturn(mockFilterBuilder);
  when(() => mockFilterBuilder.lte(any(), any())).thenReturn(mockFilterBuilder);
  when(() => mockFilterBuilder.lt(any(), any())).thenReturn(mockFilterBuilder);
  when(() => mockFilterBuilder.gt(any(), any())).thenReturn(mockFilterBuilder);
  when(
    () => mockFilterBuilder.order(any(), ascending: any(named: 'ascending')),
  ).thenReturn(mockFilterBuilder);

  // Terminal operations return Futures with data
  when(() => mockFilterBuilder.single()).thenReturn(mockSingleFilterBuilder);
  when(
    () => mockSingleFilterBuilder.then<Map<String, dynamic>>(any()),
  ).thenAnswer((invocation) async => <String, dynamic>{});

  // Configure the filter builder to return Future data when awaited
  // This handles the case where the filter builder itself is awaited
  when(
    () => mockFilterBuilder.then<List<Map<String, dynamic>>>(any()),
  ).thenAnswer((invocation) async => <Map<String, dynamic>>[]);

  // Setup single filter builder chain
  when(
    () => mockSingleFilterBuilder.eq(any(), any()),
  ).thenReturn(mockSingleFilterBuilder);
  when(
    () => mockSingleFilterBuilder.gte(any(), any()),
  ).thenReturn(mockSingleFilterBuilder);
  when(
    () => mockSingleFilterBuilder.lte(any(), any()),
  ).thenReturn(mockSingleFilterBuilder);
  when(
    () => mockSingleFilterBuilder.lt(any(), any()),
  ).thenReturn(mockSingleFilterBuilder);
  when(
    () => mockSingleFilterBuilder.gt(any(), any()),
  ).thenReturn(mockSingleFilterBuilder);
  when(
    () => mockSingleFilterBuilder.order(
      any(),
      ascending: any(named: 'ascending'),
    ),
  ).thenReturn(mockSingleFilterBuilder);

  // Default stub for currentUser
  when(() => auth.currentUser).thenReturn(null);
  when(() => auth.currentSession).thenReturn(null);

  return client;
}
