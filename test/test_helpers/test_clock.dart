import 'package:clock/clock.dart';
import 'package:fake_async/fake_async.dart'; // Correct import for FakeAsync
import 'dart:async'; // For runZoned

// Simplified helper to run a test body with a fixed time using runZoned only.
Future<T> withClock<T>(DateTime fixedTime, Future<T> Function() body) async {
  return await runZoned(() async {
    return await body();
  }, zoneValues: {#clock: Clock.fixed(fixedTime)});
}

// Original withClock, renamed to withClockWithFakeAsync, for cases where FakeAsync is explicitly desired.
Future<T> withClockWithFakeAsync<T>(
  DateTime fixedTime,
  Future<T> Function() body,
) async {
  return await FakeAsync().run((async) async {
    return await runZoned(() async {
      final result = await body();
      async
          .flushMicrotasks(); // Ensure microtasks are flushed within FakeAsync zone.
      return result;
    }, zoneValues: {#clock: Clock.fixed(fixedTime)});
  });
}

// An alternative async helper if FakeAsync is not needed at all with clock mocking.
// This is similar to the new simplified `withClock`.
Future<T> withClockAsync<T>(
  DateTime fixedTime,
  Future<T> Function() body,
) async {
  return runZoned(() => body(), zoneValues: {#clock: Clock.fixed(fixedTime)});
}
