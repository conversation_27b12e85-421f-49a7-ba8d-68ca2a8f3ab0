// test/core/services/time_service_test.dart

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:skillz/core/services/time_service.dart';

/// Mock TimeService for testing with controllable time
class MockTimeService extends TimeService {
  final DateTime _mockTime;

  MockTimeService(this._mockTime);

  @override
  DateTime now() => _mockTime;
}

void main() {
  group('TimeService', () {
    late TimeService timeService;
    final testTime = DateTime(2025, 7, 9, 14, 30, 45, 123); // 2:30:45.123 PM

    setUp(() {
      timeService = MockTimeService(testTime);
    });

    group('basic time operations', () {
      test('now returns current time', () {
        expect(timeService.now(), equals(testTime));
      });

      test('today returns date without time component', () {
        final today = timeService.today();

        expect(today.year, equals(2025));
        expect(today.month, equals(7));
        expect(today.day, equals(9));
        expect(today.hour, equals(0));
        expect(today.minute, equals(0));
        expect(today.second, equals(0));
        expect(today.millisecond, equals(0));
      });
    });

    group('dateRangeForDate', () {
      test('creates UTC date range for given date', () {
        final inputDate = DateTime(2025, 7, 9, 15, 30); // Local time
        final range = timeService.dateRangeForDate(inputDate);

        expect(range.start, equals(DateTime.utc(2025, 7, 9, 0, 0, 0)));
        expect(range.end, equals(DateTime.utc(2025, 7, 9, 23, 59, 59, 999)));
      });

      test('handles different input timezones consistently', () {
        final localDate = DateTime(2025, 7, 9, 15, 30);
        final utcDate = DateTime.utc(2025, 7, 9, 15, 30);

        final localRange = timeService.dateRangeForDate(localDate);
        final utcRange = timeService.dateRangeForDate(utcDate);

        // Both should produce the same UTC range regardless of input timezone
        expect(localRange.start, equals(utcRange.start));
        expect(localRange.end, equals(utcRange.end));
      });

      test('creates proper 24-hour range', () {
        final date = DateTime(2025, 7, 9);
        final range = timeService.dateRangeForDate(date);

        final duration = range.end.difference(range.start);
        expect(duration.inDays, equals(0));
        expect(duration.inHours, equals(23));
        expect(duration.inMinutes, equals(1439)); // 23:59
        expect(duration.inSeconds, equals(86399)); // 23:59:59
        expect(duration.inMilliseconds, equals(86399999)); // 23:59:59.999
      });
    });

    group('UTC conversion methods', () {
      test('startOfDayUtc returns start of day in UTC', () {
        final inputDate = DateTime(2025, 7, 9, 15, 30, 45);
        final startOfDay = timeService.startOfDayUtc(inputDate);

        expect(startOfDay, equals(DateTime.utc(2025, 7, 9, 0, 0, 0)));
        expect(startOfDay.isUtc, isTrue);
      });

      test('endOfDayUtc returns end of day in UTC', () {
        final inputDate = DateTime(2025, 7, 9, 15, 30, 45);
        final endOfDay = timeService.endOfDayUtc(inputDate);

        expect(endOfDay, equals(DateTime.utc(2025, 7, 9, 23, 59, 59, 999)));
        expect(endOfDay.isUtc, isTrue);
      });

      test('toUtcForStorage converts local to UTC', () {
        final localTime = DateTime(2025, 7, 9, 15, 30);
        final utcTime = timeService.toUtcForStorage(localTime);

        expect(utcTime.isUtc, isTrue);
        // The exact UTC time depends on system timezone, but it should be UTC
      });

      test('toLocalForDisplay converts UTC to local', () {
        final utcTime = DateTime.utc(2025, 7, 9, 13, 30);
        final localTime = timeService.toLocalForDisplay(utcTime);

        expect(localTime.isUtc, isFalse);
        // The exact local time depends on system timezone
      });
    });

    group('date comparison methods', () {
      test('isSameDay returns true for same calendar day', () {
        final date1 = DateTime(2025, 7, 9, 10, 30);
        final date2 = DateTime(2025, 7, 9, 15, 45);

        expect(timeService.isSameDay(date1, date2), isTrue);
      });

      test('isSameDay returns false for different days', () {
        final date1 = DateTime(2025, 7, 9, 23, 59);
        final date2 = DateTime(2025, 7, 10, 0, 1);

        expect(timeService.isSameDay(date1, date2), isFalse);
      });

      test('isSameDay ignores timezone differences', () {
        final localDate = DateTime(2025, 7, 9, 15, 30);
        final utcDate = DateTime.utc(2025, 7, 9, 10, 30);

        expect(timeService.isSameDay(localDate, utcDate), isTrue);
      });

      test('isToday returns true for current date', () {
        final todayWithDifferentTime = DateTime(2025, 7, 9, 10, 15);

        expect(timeService.isToday(todayWithDifferentTime), isTrue);
      });

      test('isToday returns false for different date', () {
        final yesterday = DateTime(2025, 7, 8, 14, 30);
        final tomorrow = DateTime(2025, 7, 10, 14, 30);

        expect(timeService.isToday(yesterday), isFalse);
        expect(timeService.isToday(tomorrow), isFalse);
      });

      test('isFutureDate returns true for future dates', () {
        final futureDate = DateTime(2025, 7, 10);

        expect(timeService.isFutureDate(futureDate), isTrue);
      });

      test('isFutureDate returns false for today and past dates', () {
        final today = DateTime(2025, 7, 9, 20, 0); // Different time, same day
        final pastDate = DateTime(2025, 7, 8);

        expect(timeService.isFutureDate(today), isFalse);
        expect(timeService.isFutureDate(pastDate), isFalse);
      });

      test('isPastDate returns true for past dates', () {
        final pastDate = DateTime(2025, 7, 8);

        expect(timeService.isPastDate(pastDate), isTrue);
      });

      test('isPastDate returns false for today and future dates', () {
        final today = DateTime(2025, 7, 9, 10, 0); // Different time, same day
        final futureDate = DateTime(2025, 7, 10);

        expect(timeService.isPastDate(today), isFalse);
        expect(timeService.isPastDate(futureDate), isFalse);
      });
    });

    group('booking-specific methods', () {
      test('getBookingCutoffTime subtracts default grace period', () {
        final cutoffTime = timeService.getBookingCutoffTime();
        final expectedCutoff = testTime.subtract(const Duration(minutes: 5));

        expect(cutoffTime, equals(expectedCutoff));
      });

      test('getBookingCutoffTime respects custom grace period', () {
        final customGracePeriod = const Duration(minutes: 10);
        final cutoffTime = timeService.getBookingCutoffTime(
          gracePeriod: customGracePeriod,
        );
        final expectedCutoff = testTime.subtract(customGracePeriod);

        expect(cutoffTime, equals(expectedCutoff));
      });
    });

    group('edge cases and error handling', () {
      test('handles leap year dates correctly', () {
        final leapYearDate = DateTime(2024, 2, 29, 15, 30);
        final range = timeService.dateRangeForDate(leapYearDate);

        expect(range.start, equals(DateTime.utc(2024, 2, 29, 0, 0, 0)));
        expect(range.end, equals(DateTime.utc(2024, 2, 29, 23, 59, 59, 999)));
      });

      test('handles year boundaries correctly', () {
        final newYearEve = DateTime(2025, 12, 31, 23, 59);
        final newYearDay = DateTime(2026, 1, 1, 0, 1);

        expect(timeService.isSameDay(newYearEve, newYearDay), isFalse);
        expect(
          timeService.isPastDate(newYearEve),
          isFalse,
        ); // Future from test time perspective
        expect(timeService.isFutureDate(newYearDay), isTrue);
      });

      test('handles daylight saving time transitions', () {
        // This test ensures our UTC-based approach handles DST correctly
        final beforeDST = DateTime(2025, 3, 8, 15, 30); // Before DST
        final afterDST = DateTime(2025, 3, 10, 15, 30); // After DST

        final rangeBefore = timeService.dateRangeForDate(beforeDST);
        final rangeAfter = timeService.dateRangeForDate(afterDST);

        // Both ranges should be exactly 24 hours in UTC
        final durationBefore = rangeBefore.end.difference(rangeBefore.start);
        final durationAfter = rangeAfter.end.difference(rangeAfter.start);

        expect(durationBefore.inMilliseconds, equals(86399999));
        expect(durationAfter.inMilliseconds, equals(86399999));
      });
    });
  });

  group('TimeService with real time', () {
    test('real TimeService returns actual current time', () {
      final realTimeService = TimeService();
      final now1 = realTimeService.now();

      // Small delay to ensure time difference
      Future.delayed(const Duration(milliseconds: 1));

      final now2 = realTimeService.now();

      // Should be very close but not identical
      expect(now2.isAfter(now1) || now2.isAtSameMomentAs(now1), isTrue);
    });
  });
}
