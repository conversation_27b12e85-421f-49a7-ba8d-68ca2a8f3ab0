// ADR-007 Validation Test - Database-Centric Slot Management
// Testing the core functionality and data transformation for the 3-slot offset bug fix

import 'package:flutter_test/flutter_test.dart';
import 'package:skillz/features/availability/domain/time_slot_info_model.dart';
import 'package:skillz/features/availability/application/database_slot_provider.dart';

void main() {
  group('ADR-007 Database-Centric Slot Management Validation', () {
    
    group('TimeSlotInfo.fromDatabaseJson', () {
      test('should parse database JSON correctly', () {
        // ARRANGE
        final databaseJson = {
          'id': '123e4567-e89b-12d3-a456-426614174000',
          'slot_date': '2025-06-20',
          'start_time': '09:00:00',
          'end_time': '10:00:00',
          'is_booked': false,
          'booking_id': null,
          'price_per_hour': 5000.0,
        };

        // ACT
        final slot = TimeSlotInfo.fromDatabaseJson(databaseJson);

        // ASSERT
        expect(slot.startTime, equals(DateTime(2025, 6, 20, 9, 0)));
        expect(slot.endTime, equals(DateTime(2025, 6, 20, 10, 0)));
        expect(slot.isBooked, isFalse);
        expect(slot.bookingId, isNull);
      });

      test('should handle booked slots with booking_id', () {
        // ARRANGE
        final databaseJson = {
          'id': '123e4567-e89b-12d3-a456-426614174000',
          'slot_date': '2025-06-20',
          'start_time': '12:00:00',
          'end_time': '13:00:00',
          'is_booked': true,
          'booking_id': 42,
          'price_per_hour': 5000.0,
        };

        // ACT
        final slot = TimeSlotInfo.fromDatabaseJson(databaseJson);

        // ASSERT
        expect(slot.startTime, equals(DateTime(2025, 6, 20, 12, 0)));
        expect(slot.endTime, equals(DateTime(2025, 6, 20, 13, 0)));
        expect(slot.isBooked, isTrue);
        expect(slot.bookingId, equals('42'));
      });

      test('should handle missing optional fields gracefully', () {
        // ARRANGE
        final databaseJson = {
          'id': '123e4567-e89b-12d3-a456-426614174000',
          'slot_date': '2025-06-20',
          'start_time': '09:00:00',
          'end_time': '10:00:00',
          'price_per_hour': 5000.0,
          // Missing: is_booked, booking_id
        };

        // ACT
        final slot = TimeSlotInfo.fromDatabaseJson(databaseJson);

        // ASSERT
        expect(slot.isBooked, isFalse); // Should default to false
        expect(slot.bookingId, isNull);
        expect(slot.startTime, equals(DateTime(2025, 6, 20, 9, 0)));
      });
    });

    group('ADR-007 Bug Fix Validation - 3-Slot Offset', () {
      test('should accurately map database slot status without offset', () {
        // ARRANGE - This test validates that the 3-slot offset bug is fixed
        // In the old system, if slot 3 was booked, slots 0, 1, 2 would incorrectly show as booked
        // With the database-centric approach, only the actually booked slot should show as booked
        final slotsWithSpecificBooking = [
          {
            'id': '123e4567-e89b-12d3-a456-426614174000',
            'pitch_id': 1,
            'slot_date': '2025-06-20',
            'start_time': '09:00:00',
            'end_time': '10:00:00',
            'is_booked': false,
            'booking_id': null,
            'price_per_hour': 5000.0,
          },
          {
            'id': '123e4567-e89b-12d3-a456-426614174001',
            'pitch_id': 1,
            'slot_date': '2025-06-20',
            'start_time': '10:00:00',
            'end_time': '11:00:00',
            'is_booked': false,
            'booking_id': null,
            'price_per_hour': 5000.0,
          },
          {
            'id': '123e4567-e89b-12d3-a456-426614174002',
            'pitch_id': 1,
            'slot_date': '2025-06-20',
            'start_time': '11:00:00',
            'end_time': '12:00:00',
            'is_booked': false,
            'booking_id': null,
            'price_per_hour': 5000.0,
          },
          {
            'id': '123e4567-e89b-12d3-a456-426614174003',
            'pitch_id': 1,
            'slot_date': '2025-06-20',
            'start_time': '12:00:00', // This specific slot is booked
            'end_time': '13:00:00',
            'is_booked': true,
            'booking_id': 99,
            'price_per_hour': 5000.0,
          },
        ];

        // ACT - Convert database response to TimeSlotInfo objects
        final result = slotsWithSpecificBooking
            .map((json) => TimeSlotInfo.fromDatabaseJson(json))
            .toList();

        // ASSERT - Verify NO 3-slot offset
        expect(result, hasLength(4));

        // Only slot 3 (index 3, 12:00-13:00) should be booked
        expect(result[0].isBooked, isFalse, reason: 'Slot 0 (09:00-10:00) should NOT be booked');
        expect(result[1].isBooked, isFalse, reason: 'Slot 1 (10:00-11:00) should NOT be booked');
        expect(result[2].isBooked, isFalse, reason: 'Slot 2 (11:00-12:00) should NOT be booked');
        expect(result[3].isBooked, isTrue, reason: 'Slot 3 (12:00-13:00) should be booked');

        // Verify the correct slot details
        expect(result[3].startTime.hour, equals(12));
        expect(result[3].endTime.hour, equals(13));
        expect(result[3].bookingId, equals('99'));
      });

      test('should handle multiple booked slots correctly', () {
        // ARRANGE - Test multiple bookings to ensure no cross-contamination
        final slotsWithMultipleBookings = [
          {
            'id': '123e4567-e89b-12d3-a456-426614174000',
            'slot_date': '2025-06-20',
            'start_time': '09:00:00',
            'end_time': '10:00:00',
            'is_booked': true,
            'booking_id': 100,
            'price_per_hour': 5000.0,
          },
          {
            'id': '123e4567-e89b-12d3-a456-426614174001',
            'slot_date': '2025-06-20',
            'start_time': '10:00:00',
            'end_time': '11:00:00',
            'is_booked': false,
            'booking_id': null,
            'price_per_hour': 5000.0,
          },
          {
            'id': '123e4567-e89b-12d3-a456-426614174002',
            'slot_date': '2025-06-20',
            'start_time': '11:00:00',
            'end_time': '12:00:00',
            'is_booked': true,
            'booking_id': 200,
            'price_per_hour': 5000.0,
          },
        ];

        // ACT
        final result = slotsWithMultipleBookings
            .map((json) => TimeSlotInfo.fromDatabaseJson(json))
            .toList();

        // ASSERT - Each slot should have its own correct status
        expect(result[0].isBooked, isTrue);
        expect(result[0].bookingId, equals('100'));
        expect(result[1].isBooked, isFalse);
        expect(result[1].bookingId, isNull);
        expect(result[2].isBooked, isTrue);
        expect(result[2].bookingId, equals('200'));
      });
    });

    group('DatabaseSlotException', () {
      test('should create exception with message and original error', () {
        // ARRANGE
        final originalError = Exception('Original error');
        
        // ACT
        final exception = DatabaseSlotException('Test error message', originalError);

        // ASSERT
        expect(exception.message, equals('Test error message'));
        expect(exception.originalError, equals(originalError));
        expect(exception.toString(), equals('DatabaseSlotException: Test error message'));
      });

      test('should create exception with message only', () {
        // ACT
        final exception = DatabaseSlotException('Test error message');

        // ASSERT
        expect(exception.message, equals('Test error message'));
        expect(exception.originalError, isNull);
        expect(exception.toString(), equals('DatabaseSlotException: Test error message'));
      });
    });

    group('TimeSlotInfo Enhanced Features', () {
      test('should identify optimistic bookings correctly', () {
        // ARRANGE
        final optimisticSlot = TimeSlotInfo(
          startTime: DateTime(2025, 6, 20, 9, 0),
          endTime: DateTime(2025, 6, 20, 10, 0),
          isBooked: true,
          bookingId: 'optimistic',
        );

        // ACT & ASSERT
        expect(optimisticSlot.isOptimisticallyBooked, isTrue);
        expect(optimisticSlot.isConfirmedBooked, isFalse);
        expect(optimisticSlot.displayStatus, equals('Booking...'));
        expect(optimisticSlot.canBeBooked, isFalse);
      });

      test('should identify past slots correctly', () {
        // ARRANGE
        final pastSlot = TimeSlotInfo(
          startTime: DateTime(2025, 6, 20, 9, 0),
          endTime: DateTime(2025, 6, 20, 10, 0),
          isBooked: true,
          bookingId: 'past_slot',
        );

        // ACT & ASSERT
        expect(pastSlot.isPastSlot, isTrue);
        expect(pastSlot.isConfirmedBooked, isFalse);
        expect(pastSlot.displayStatus, equals('Past'));
        expect(pastSlot.canBeBooked, isFalse);
      });

      test('should identify confirmed bookings correctly', () {
        // ARRANGE
        final confirmedSlot = TimeSlotInfo(
          startTime: DateTime(2025, 6, 20, 9, 0),
          endTime: DateTime(2025, 6, 20, 10, 0),
          isBooked: true,
          bookingId: '123',
        );

        // ACT & ASSERT
        expect(confirmedSlot.isConfirmedBooked, isTrue);
        expect(confirmedSlot.isOptimisticallyBooked, isFalse);
        expect(confirmedSlot.isPastSlot, isFalse);
        expect(confirmedSlot.displayStatus, equals('Booked'));
        expect(confirmedSlot.canBeBooked, isFalse);
      });

      test('should identify available slots correctly', () {
        // ARRANGE
        final availableSlot = TimeSlotInfo(
          startTime: DateTime(2025, 6, 20, 9, 0),
          endTime: DateTime(2025, 6, 20, 10, 0),
          isBooked: false,
          bookingId: null,
        );

        // ACT & ASSERT
        expect(availableSlot.isConfirmedBooked, isFalse);
        expect(availableSlot.isOptimisticallyBooked, isFalse);
        expect(availableSlot.isPastSlot, isFalse);
        expect(availableSlot.displayStatus, equals('Available'));
        expect(availableSlot.canBeBooked, isTrue);
      });
    });
  });
}
