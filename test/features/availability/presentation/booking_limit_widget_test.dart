import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart'; // Ensure GoRouter is imported
import 'package:skillz/features/availability/domain/pitch_settings_model.dart';
import 'package:skillz/features/availability/domain/time_slot_info_model.dart';
import 'package:skillz/features/availability/presentation/widgets/slot_list_item.dart';
import 'package:skillz/features/booking/data/booking_repository.dart';
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/availability/application/availability_service.dart';
import '../../../test_helpers/widget_test_helper.dart';
import '../../../test_helpers/supabase_mock_helper.dart';

void main() {
  setUpAll(() async {
    // Initialize test helpers using the established pattern
    await setupWidgetTest();
  });

  tearDownAll(() {
    tearDownSupabaseMocks();
  });

  // Mock data for PitchSettings
  final mockPitchSettings = [
    PitchSettings(
      id: 1,
      name: 'Test Pitch',
      openTime: '09:00',
      closeTime: '22:00',
      slotDurationMinutes: 60,
      cancellationWindowHours: 24,
      pricePerHour: 5000.0, // Default test price
      maxBookingsPerUser: 4, // Default test booking limit
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  // Create helper function to build the widget under test with required providers
  Widget createTestWidget({
    required TimeSlotInfo slot,
    AsyncValue<List<Booking>>? userBookingsAsyncValue,
    DateTime? selectedDate,
    int? selectedPitchId,
    List<PitchSettings>? customPitchSettings,
  }) {
    final effectiveDate = selectedDate ?? DateTime.now();
    final effectivePitchId = selectedPitchId ?? 1;
    final effectiveUserBookingsAsyncValue =
        userBookingsAsyncValue ?? const AsyncValue.data(<Booking>[]);
    final effectivePitchSettings = customPitchSettings ?? mockPitchSettings;

    // Create a simple GoRouter for testing navigation
    final testGoRouter = GoRouter(
      initialLocation: '/',
      routes: [
        GoRoute(
          path: '/',
          builder:
              (context, state) => Scaffold(
                body: SlotListItem(
                  slot: slot,
                  selectedDate: effectiveDate,
                  selectedPitchId: effectivePitchId,
                ),
              ),
        ),
        GoRoute(
          path: '/my_bookings', // Mock route for navigation testing
          builder:
              (context, state) =>
                  const Scaffold(body: Text('My Bookings Screen')),
        ),
        GoRoute(
          path: '/booking-confirmation', // Mock route for navigation testing
          builder:
              (context, state) =>
                  const Scaffold(body: Text('Booking Confirmation Screen')),
        ),
      ],
    );

    return ProviderScope(
      overrides: [
        // Include base test overrides
        ...testProviderOverrides,
        // Updated override for userBookingsProvider
        userBookingsProvider.overrideWith((ref) {
          // Directly return the desired AsyncValue state
          if (effectiveUserBookingsAsyncValue.isLoading) {
            // For loading, return a future that won't complete immediately
            // or a specific loading state if your provider handles it.
            // For simplicity here, we'll assume the widget's .when handles AsyncLoading.
            // A common pattern is to return a Future that never completes or completes after a delay.
            // However, the widget should react to AsyncLoading() directly.
            // Let's ensure the provider emits AsyncLoading() then the actual data if that's the flow.
            // For now, let's stick to what availability_screen_test does:
            return Future<List<Booking>>.delayed(
              const Duration(milliseconds: 50), // Simulate network delay
              () => effectiveUserBookingsAsyncValue.value ?? <Booking>[],
            );
          }
          if (effectiveUserBookingsAsyncValue.hasError) {
            return Future.error(
              effectiveUserBookingsAsyncValue.error!,
              effectiveUserBookingsAsyncValue.stackTrace ?? StackTrace.empty,
            );
          }
          // If AsyncData, provide the data directly as a completed Future.
          return Future.value(
            effectiveUserBookingsAsyncValue.value ?? <Booking>[],
          );
        }),
        allPitchesProvider.overrideWith(
          (ref) => Future.value(effectivePitchSettings),
        ),
        selectedPitchIdProvider.overrideWith((ref) => effectivePitchId),
        // Mock pitchSettingsProvider correctly
        // Ensure it returns a PitchSettings object, not a list
        pitchSettingsProvider(effectivePitchId).overrideWith((ref) {
          final settings = effectivePitchSettings.firstWhere(
            (p) => p.id == effectivePitchId,
            orElse: () => effectivePitchSettings.first, // Fallback if not found
          );
          return Future.value(settings);
        }),
      ],
      child: MaterialApp.router(routerConfig: testGoRouter),
    );
  }

  // Create test TimeSlotInfo for a current slot
  TimeSlotInfo createCurrentTimeSlot() {
    final now = DateTime.now();
    return TimeSlotInfo(
      startTime: now.add(const Duration(hours: 1)),
      endTime: now.add(const Duration(hours: 2)),
      isBooked: false,
    );
  }

  // Create test bookings for the limit scenario
  List<Booking> createMaximumActiveBookings() {
    final now = DateTime.now();
    return List.generate(
      4,
      (index) => Booking(
        id: index + 1,
        userId: 'test-user',
        pitchId: 1,
        slotStartTime: now.add(Duration(days: index + 1, hours: 10)),
        slotEndTime: now.add(Duration(days: index + 1, hours: 11)),
        status: BookingStatus.confirmed,
        createdAt: now,
        updatedAt: now,
      ),
    );
  }

  group('SlotListItem Frontend Booking Limit Display Tests', () {
    testWidgets(
      'Shows "Limit" button when user has reached booking limit from backend settings',
      (tester) async {
        // Arrange - Create test data with maximum bookings
        final timeSlot = createCurrentTimeSlot();
        final maxBookings = createMaximumActiveBookings();

        await tester.pumpWidget(
          createTestWidget(
            slot: timeSlot,
            userBookingsAsyncValue: AsyncData(maxBookings),
          ),
        );

        await tester.pumpAndSettle();

        // Assert - Should show limit button when user has max bookings
        expect(find.textContaining('Limit (4/4)'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);

        // Button should be enabled (to show SnackBar when tapped)
        final button = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(button.onPressed, isNotNull);
      },
    );

    testWidgets(
      'Tapping limit button shows SnackBar with "View Bookings" action',
      (tester) async {
        // Arrange - Create test data with max bookings
        final timeSlot = createCurrentTimeSlot();
        final maxBookings = createMaximumActiveBookings();

        // Build the widget with navigation tracking
        await tester.pumpWidget(
          createTestWidget(
            slot: timeSlot,
            userBookingsAsyncValue: AsyncData(maxBookings),
          ),
        );
        await tester.pumpAndSettle(); // Ensure all frames are processed

        // Act - Tap the limit button to trigger SnackBar
        await tester.tap(find.byType(ElevatedButton));
        await tester.pumpAndSettle(); // Process tap and SnackBar appearance

        // Assert - Verify SnackBar appears with correct message
        expect(find.byType(SnackBar), findsOneWidget);
        expect(
          find.text('You have reached the maximum limit of 4 active bookings.'),
          findsOneWidget,
        );
        expect(find.text('View Bookings'), findsOneWidget);

        // Now tap the "View Bookings" action in the SnackBar
        await tester.tap(find.text('View Bookings'));
        await tester.pumpAndSettle();

        // For testing purposes, we'll look for lack of exceptions
        expect(tester.takeException(), isNull);
      },
    );

    testWidgets('Shows "Book" button when user has not reached booking limit', (
      tester,
    ) async {
      // Arrange - Create test data with fewer than max bookings
      final timeSlot = createCurrentTimeSlot();
      final underLimitBookings = createMaximumActiveBookings().sublist(
        0,
        3,
      ); // 3 out of 4

      // Build the widget with 3 active bookings
      await tester.pumpWidget(
        createTestWidget(
          slot: timeSlot,
          userBookingsAsyncValue: AsyncData(underLimitBookings),
        ),
      );
      await tester.pumpAndSettle(); // Ensure all frames are processed

      // Assert - Should show "Book" button when under limit
      expect(find.text('Book'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);

      // Button should be enabled
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNotNull);
    });

    testWidgets('Limit display uses dynamic value from pitch settings', (
      tester,
    ) async {
      // Arrange - Create test data and modify pitch settings to have different limit
      final timeSlot = createCurrentTimeSlot();
      final maxBookings = createMaximumActiveBookings();

      // Create pitch settings with different limit (e.g., 3 instead of 4)
      final customPitchSettings = [
        PitchSettings(
          id: 1,
          name: 'Test Pitch',
          openTime: '09:00',
          closeTime: '22:00',
          slotDurationMinutes: 60,
          cancellationWindowHours: 24,
          pricePerHour: 5000.0,
          maxBookingsPerUser: 3, // Different limit
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      await tester.pumpWidget(
        createTestWidget(
          slot: timeSlot,
          userBookingsAsyncValue: AsyncData(
            maxBookings.sublist(0, 3),
          ), // 3 bookings
          customPitchSettings: customPitchSettings,
        ),
      );
      await tester.pumpAndSettle();

      // Assert - Should show limit reached with custom limit value
      expect(find.textContaining('Limit (3/3)'), findsOneWidget);
    });
  });
}
