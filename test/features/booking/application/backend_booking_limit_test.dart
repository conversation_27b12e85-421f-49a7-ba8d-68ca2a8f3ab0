import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skillz/features/booking/application/optimistic_booking_service.dart';
import 'package:skillz/features/booking/domain/booking_exceptions.dart';
import 'package:skillz/features/auth/application/auth_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:mocktail/mocktail.dart';

class MockSupabaseClient extends Mock implements SupabaseClient {}

class MockFunctionsClient extends Mock implements FunctionsClient {}

class MockAuthService extends Mock implements GoTrueClient {}

class MockUser extends Mock implements User {}

class MockFunctionResponse extends Mock implements FunctionResponse {}

void main() {
  group('Backend Booking Limit Enforcement Tests', () {
    late MockSupabaseClient mockSupabaseClient;
    late MockFunctionsClient mockFunctionsClient;
    late MockAuthService mockAuthService;
    late MockUser mockUser;
    late ProviderContainer container;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockFunctionsClient = MockFunctionsClient();
      mockAuthService = MockAuthService();
      mockUser = MockUser();

      // Setup default mocks
      when(() => mockSupabaseClient.functions).thenReturn(mockFunctionsClient);
      when(() => mockSupabaseClient.auth).thenReturn(mockAuthService);
      when(() => mockAuthService.currentUser).thenReturn(mockUser);
      when(() => mockUser.id).thenReturn('test-user-id');

      container = ProviderContainer(
        overrides: [
          supabaseClientProvider.overrideWithValue(mockSupabaseClient),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('Handles BOOKING_LIMIT_EXCEEDED error from backend properly', (
      tester,
    ) async {
      // Arrange - Mock backend response for booking limit exceeded
      final mockResponse = MockFunctionResponse();
      when(() => mockResponse.status).thenReturn(409);
      when(() => mockResponse.data).thenReturn({
        'error': 'BOOKING_LIMIT_EXCEEDED',
        'message': 'You can only have 4 active bookings at a time',
      });

      when(
        () => mockFunctionsClient.invoke(
          'create_booking_atomic',
          body: any(named: 'body'),
        ),
      ).thenAnswer((_) async => mockResponse);

      // Act - Attempt to create booking
      final service = container.read(optimisticBookingServiceProvider.notifier);
      await service.createBookingOptimistic(
        pitchId: 1,
        slotStartTime: DateTime.now().add(const Duration(hours: 1)),
        slotEndTime: DateTime.now().add(const Duration(hours: 2)),
      );

      // Assert - Check that BookingLimitReachedException was properly handled
      final state = container.read(optimisticBookingServiceProvider);
      expect(state.status, OptimisticBookingStatus.error);
      expect(state.exception, isA<BookingLimitReachedException>());
      expect(state.message, 'You can only have 4 active bookings at a time');
    });

    testWidgets('Handles SLOT_UNAVAILABLE error from backend properly', (
      tester,
    ) async {
      // Arrange - Mock backend response for slot unavailable
      final mockResponse = MockFunctionResponse();
      when(() => mockResponse.status).thenReturn(409);
      when(() => mockResponse.data).thenReturn({
        'error': 'SLOT_UNAVAILABLE',
        'message': 'This time slot was just booked by another user',
      });

      when(
        () => mockFunctionsClient.invoke(
          'create_booking_atomic',
          body: any(named: 'body'),
        ),
      ).thenAnswer((_) async => mockResponse);

      // Act - Attempt to create booking
      final service = container.read(optimisticBookingServiceProvider.notifier);
      await service.createBookingOptimistic(
        pitchId: 1,
        slotStartTime: DateTime.now().add(const Duration(hours: 1)),
        slotEndTime: DateTime.now().add(const Duration(hours: 2)),
      );

      // Assert - Check that SlotUnavailableException was properly handled
      final state = container.read(optimisticBookingServiceProvider);
      expect(state.status, OptimisticBookingStatus.conflict);
      expect(state.exception, isA<SlotUnavailableException>());
      expect(state.message, 'This time slot was just booked by another user');
    });

    testWidgets('Successfully creates booking when under limit', (
      tester,
    ) async {
      // Arrange - Mock successful backend response
      final mockResponse = MockFunctionResponse();
      when(() => mockResponse.status).thenReturn(201);
      when(() => mockResponse.data).thenReturn({
        'success': true,
        'booking': {'id': 123, 'status': 'confirmed'},
        'message': 'Booking created successfully',
      });

      when(
        () => mockFunctionsClient.invoke(
          'create_booking_atomic',
          body: any(named: 'body'),
        ),
      ).thenAnswer((_) async => mockResponse);

      // Act - Attempt to create booking
      final service = container.read(optimisticBookingServiceProvider.notifier);
      await service.createBookingOptimistic(
        pitchId: 1,
        slotStartTime: DateTime.now().add(const Duration(hours: 1)),
        slotEndTime: DateTime.now().add(const Duration(hours: 2)),
      );

      // Assert - Check that booking was successfully created
      final state = container.read(optimisticBookingServiceProvider);
      expect(state.status, OptimisticBookingStatus.success);
      expect(state.exception, isNull);
    });
  });
}
