import 'dart:async'; // Import Completer

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:skillz/features/booking/data/booking_repository.dart'; // For userBookingsProvider
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/booking/presentation/my_bookings_screen.dart';
import '../../../test_helpers/widget_test_helper.dart'; // Corrected import path
import '../../../test_helpers/supabase_mock_helper.dart'; // For tearDownSupabaseMocks

void main() {
  setUp(() async {
    // Set up mock Supabase and other test helpers
    await setupWidgetTest();
  });

  tearDown(() {
    tearDownSupabaseMocks();
  });

  group('MyBookingsScreen Widget Tests', () {
    testWidgets('shows loading indicator when bookings are being fetched', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...testProviderOverrides,
            userBookingsProvider.overrideWith(
              // Return a Future that will keep the provider in a loading state
              (ref) => Completer<List<Booking>>().future,
            ),
          ],
          child: testableWidget(child: const MyBookingsScreen()),
        ),
      );
      // You might need a small pump to allow the UI to react to the loading state
      await tester.pump();
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('shows list of bookings when data is available', (
      WidgetTester tester,
    ) async {
      // Use real current time and ensure booking is truly in the future
      final now = DateTime.now();
      final bookings = [
        Booking(
          id: 1, // This ID is not directly displayed in the default ListTile
          userId: 'user1',
          pitchId: 101, // This is displayed
          slotStartTime: now.add(const Duration(days: 3)), // 3 days from now
          slotEndTime: now.add(const Duration(days: 3, hours: 1)),
          status: BookingStatus.confirmed,
          createdAt: now,
          updatedAt: now,
        ),
        // Add more mock bookings if needed
      ];

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...testProviderOverrides,
            // Override to return a successfully completed Future with mock data
            userBookingsProvider.overrideWith((ref) => Future.value(bookings)),
          ],
          child: testableWidget(child: const MyBookingsScreen()),
        ),
      );

      // Pump and settle to allow futures to complete and UI to rebuild
      await tester.pumpAndSettle();

      // Assert that the bookings are displayed
      // Corrected assertion: Find text based on what's actually rendered
      expect(find.textContaining('Pitch: 101'), findsOneWidget);
      // Check for part of the title or subtitle if more specific targeting is needed
      expect(find.textContaining('Date:'), findsOneWidget);
      // You'll also want to ensure the loading indicator is NOT present
      expect(find.byType(CircularProgressIndicator), findsNothing);
    });

    testWidgets(
      'shows error message and retry button when fetching bookings fails',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              ...testProviderOverrides,
              userBookingsProvider.overrideWith(
                // Return a Future that completes with an error
                (ref) => Future.error('Failed to load', StackTrace.current),
              ),
            ],
            child: testableWidget(child: const MyBookingsScreen()),
          ),
        );
        // Pump and settle to allow the Future to complete with an error
        // and the UI to rebuild accordingly.
        await tester.pumpAndSettle();

        // Ensure the loading indicator is not present
        expect(find.byType(CircularProgressIndicator), findsNothing);
        // Corrected assertion for error message
        expect(
          find.textContaining('Error fetching bookings: Failed to load'),
          findsOneWidget,
        );
        // Check for a retry button (adjust if your UI implements it differently)
        expect(find.widgetWithText(ElevatedButton, 'Retry'), findsOneWidget);
      },
    );

    testWidgets(
      'tapping retry button re-fetches bookings and can transition to success',
      (WidgetTester tester) async {
        int fetchAttemptCount = 0;
        final now = DateTime.now(); // Use real current time
        final List<Booking> successfulBookings = [
          Booking(
            id: 3,
            userId: 'user1',
            pitchId: 103,
            slotStartTime: now.add(const Duration(days: 5)),
            slotEndTime: now.add(const Duration(days: 5, hours: 1)),
            status: BookingStatus.confirmed,
            createdAt: now,
            updatedAt: now,
          ),
        ];

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              ...testProviderOverrides,
              userBookingsProvider.overrideWith((ref) {
                fetchAttemptCount++;
                if (fetchAttemptCount == 1) {
                  // First attempt: return a Future that completes with an error
                  return Future.error('Network error', StackTrace.current);
                } else {
                  // Second attempt (after retry): return a Future that completes with data
                  return Future.value(successfulBookings);
                }
              }),
            ],
            child: testableWidget(child: const MyBookingsScreen()),
          ),
        );

        // Initial state: error
        await tester
            .pumpAndSettle(); // Allow the first Future.error to complete
        // Corrected assertion for initial error message
        expect(
          find.textContaining('Error fetching bookings: Network error'),
          findsOneWidget,
        ); // Ensure UI shows error
        expect(find.widgetWithText(ElevatedButton, 'Retry'), findsOneWidget);
        expect(fetchAttemptCount, 1);

        // Tap retry button
        await tester.tap(find.widgetWithText(ElevatedButton, 'Retry'));
        // When retry is tapped, the provider is refreshed.
        // pumpAndSettle will wait for the new Future (Future.value) to complete.
        await tester.pumpAndSettle();

        // Assert new state: success
        expect(fetchAttemptCount, 2); // Provider was called again
        expect(
          find.textContaining('Error fetching bookings: Network error'),
          findsNothing,
        ); // Error message should be gone
        expect(
          find.byType(ListView),
          findsOneWidget,
        ); // Assuming ListView is shown on success
        // Adjust based on your MyBookingsScreen's success UI
        // Corrected assertion for successful data
        expect(
          find.textContaining(
            'Pitch: 103',
          ), // Check for data from successful fetch
          findsOneWidget,
        );
      },
    );    testWidgets(
      'correctly categorizes bookings based on slot start time',
      (WidgetTester tester) async {
        // Use real current time since widget uses DateTime.now()
        final now = DateTime.now();
        
        final bookings = [
          // Past booking: started 1 hour ago (still ongoing but started in past)
          Booking(
            id: 1,
            userId: 'user1',
            pitchId: 101,
            slotStartTime: now.subtract(const Duration(hours: 1)), // 1 hour ago (past)
            slotEndTime: now.add(const Duration(minutes: 30)), // 30 min future (future)
            status: BookingStatus.confirmed,
            createdAt: now.subtract(const Duration(days: 1)),
            updatedAt: now.subtract(const Duration(days: 1)),
          ),
          // Upcoming booking: starts in 2 hours
          Booking(
            id: 2,
            userId: 'user1',
            pitchId: 102,
            slotStartTime: now.add(const Duration(hours: 2)), // 2 hours future (future)
            slotEndTime: now.add(const Duration(hours: 3)), // 3 hours future (future)
            status: BookingStatus.confirmed,
            createdAt: now.subtract(const Duration(days: 1)),
            updatedAt: now.subtract(const Duration(days: 1)),
          ),
          // Past booking: completely finished
          Booking(
            id: 3,
            userId: 'user1',
            pitchId: 103,
            slotStartTime: now.subtract(const Duration(hours: 3)), // 3 hours ago (past)
            slotEndTime: now.subtract(const Duration(hours: 2)), // 2 hours ago (past)
            status: BookingStatus.confirmed,
            createdAt: now.subtract(const Duration(days: 2)),
            updatedAt: now.subtract(const Duration(days: 2)),
          ),
          // Upcoming booking: tomorrow
          Booking(
            id: 4,
            userId: 'user1',
            pitchId: 104,
            slotStartTime: now.add(const Duration(days: 1)), // Tomorrow (future)
            slotEndTime: now.add(const Duration(days: 1, hours: 1)), // Tomorrow + 1 hour
            status: BookingStatus.confirmed,
            createdAt: now.subtract(const Duration(days: 1)),
            updatedAt: now.subtract(const Duration(days: 1)),
          ),
        ];

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              ...testProviderOverrides,
              userBookingsProvider.overrideWith((ref) => Future.value(bookings)),
            ],
            child: testableWidget(child: const MyBookingsScreen()),
          ),
        );

        await tester.pumpAndSettle();

        // Verify that the TabBar exists
        expect(find.text('Upcoming'), findsOneWidget);
        expect(find.text('Past'), findsOneWidget);

        // By default, we should be on the Upcoming tab
        // Upcoming bookings should only include bookings where slotStartTime is in the future
        // This should include bookings #2 and #4 (IDs 2 and 4)
        expect(find.textContaining('Pitch: 102'), findsOneWidget); // Booking #2
        expect(find.textContaining('Pitch: 104'), findsOneWidget); // Booking #4

        // Past bookings should not be visible in Upcoming tab
        expect(find.textContaining('Pitch: 101'), findsNothing); // Booking #1 (ongoing but started in past)
        expect(find.textContaining('Pitch: 103'), findsNothing); // Booking #3 (completely past)

        // Switch to Past tab
        await tester.tap(find.text('Past'));
        await tester.pumpAndSettle();

        // Past bookings should include bookings where slotStartTime is in the past
        // This should include bookings #1 and #3 (IDs 1 and 3)
        expect(find.textContaining('Pitch: 101'), findsOneWidget); // Booking #1 (started in past)
        expect(find.textContaining('Pitch: 103'), findsOneWidget); // Booking #3 (completely past)

        // Upcoming bookings should not be visible in Past tab
        expect(find.textContaining('Pitch: 102'), findsNothing); // Booking #2
        expect(find.textContaining('Pitch: 104'), findsNothing); // Booking #4
      },
    );
  });
}
