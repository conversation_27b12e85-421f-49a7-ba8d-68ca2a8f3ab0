import 'dart:async'; // Import Completer

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:skillz/features/booking/data/booking_repository.dart'; // For userBookingsProvider
import 'package:skillz/features/booking/domain/booking_model.dart';
import 'package:skillz/features/booking/presentation/my_bookings_screen.dart';
import '../../../test_helpers/widget_test_helper.dart'; // Corrected import path
import '../../../test_helpers/supabase_mock_helper.dart'; // For tearDownSupabaseMocks

void main() {
  setUp(() async {
    // Set up mock Supabase and other test helpers
    await setupWidgetTest();
  });

  tearDown(() {
    tearDownSupabaseMocks();
  });

  group('MyBookingsScreen Widget Tests', () {
    testWidgets('shows loading indicator when bookings are being fetched', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...testProviderOverrides,
            userBookingsProvider.overrideWith(
              // Return a Future that will keep the provider in a loading state
              (ref) => Completer<List<Booking>>().future,
            ),
          ],
          child: testableWidget(child: const MyBookingsScreen()),
        ),
      );
      // You might need a small pump to allow the UI to react to the loading state
      await tester.pump();
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('shows list of bookings when data is available', (
      WidgetTester tester,
    ) async {
      // Use real current time and ensure booking is truly in the future
      final now = DateTime.now();
      final bookings = [
        Booking(
          id: 1, // This ID is not directly displayed in the default ListTile
          userId: 'user1',
          pitchId: 101, // This is displayed
          slotStartTime: now.add(const Duration(days: 3)), // 3 days from now
          slotEndTime: now.add(const Duration(days: 3, hours: 1)),
          status: BookingStatus.confirmed,
          createdAt: now,
          updatedAt: now,
        ),
        // Add more mock bookings if needed
      ];

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...testProviderOverrides,
            // Override to return a successfully completed Future with mock data
            userBookingsProvider.overrideWith((ref) => Future.value(bookings)),
          ],
          child: testableWidget(child: const MyBookingsScreen()),
        ),
      );

      // Pump and settle to allow futures to complete and UI to rebuild
      await tester.pumpAndSettle();

      // Assert that the bookings are displayed
      // Corrected assertion: Find text based on what's actually rendered
      expect(find.textContaining('Pitch: 101'), findsOneWidget);
      // Check for part of the title or subtitle if more specific targeting is needed
      expect(find.textContaining('Date:'), findsOneWidget);
      // You'll also want to ensure the loading indicator is NOT present
      expect(find.byType(CircularProgressIndicator), findsNothing);
    });

    testWidgets(
      'shows error message and retry button when fetching bookings fails',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              ...testProviderOverrides,
              userBookingsProvider.overrideWith(
                // Return a Future that completes with an error
                (ref) => Future.error('Failed to load', StackTrace.current),
              ),
            ],
            child: testableWidget(child: const MyBookingsScreen()),
          ),
        );
        // Pump and settle to allow the Future to complete with an error
        // and the UI to rebuild accordingly.
        await tester.pumpAndSettle();

        // Ensure the loading indicator is not present
        expect(find.byType(CircularProgressIndicator), findsNothing);
        // Corrected assertion for error message
        expect(
          find.textContaining('Error fetching bookings: Failed to load'),
          findsOneWidget,
        );
        // Check for a retry button (adjust if your UI implements it differently)
        expect(find.widgetWithText(ElevatedButton, 'Retry'), findsOneWidget);
      },
    );

    testWidgets(
      'tapping retry button re-fetches bookings and can transition to success',
      (WidgetTester tester) async {
        int fetchAttemptCount = 0;
        final now = DateTime.now(); // Use real current time
        final List<Booking> successfulBookings = [
          Booking(
            id: 3,
            userId: 'user1',
            pitchId: 103,
            slotStartTime: now.add(const Duration(days: 5)),
            slotEndTime: now.add(const Duration(days: 5, hours: 1)),
            status: BookingStatus.confirmed,
            createdAt: now,
            updatedAt: now,
          ),
        ];

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              ...testProviderOverrides,
              userBookingsProvider.overrideWith((ref) {
                fetchAttemptCount++;
                if (fetchAttemptCount == 1) {
                  // First attempt: return a Future that completes with an error
                  return Future.error('Network error', StackTrace.current);
                } else {
                  // Second attempt (after retry): return a Future that completes with data
                  return Future.value(successfulBookings);
                }
              }),
            ],
            child: testableWidget(child: const MyBookingsScreen()),
          ),
        );

        // Initial state: error
        await tester
            .pumpAndSettle(); // Allow the first Future.error to complete
        // Corrected assertion for initial error message
        expect(
          find.textContaining('Error fetching bookings: Network error'),
          findsOneWidget,
        ); // Ensure UI shows error
        expect(find.widgetWithText(ElevatedButton, 'Retry'), findsOneWidget);
        expect(fetchAttemptCount, 1);

        // Tap retry button
        await tester.tap(find.widgetWithText(ElevatedButton, 'Retry'));
        // When retry is tapped, the provider is refreshed.
        // pumpAndSettle will wait for the new Future (Future.value) to complete.
        await tester.pumpAndSettle();

        // Assert new state: success
        expect(fetchAttemptCount, 2); // Provider was called again
        expect(
          find.textContaining('Error fetching bookings: Network error'),
          findsNothing,
        ); // Error message should be gone
        expect(
          find.byType(ListView),
          findsOneWidget,
        ); // Assuming ListView is shown on success
        // Adjust based on your MyBookingsScreen's success UI
        // Corrected assertion for successful data
        expect(
          find.textContaining(
            'Pitch: 103',
          ), // Check for data from successful fetch
          findsOneWidget,
        );
      },
    );
  });
}
