# Known Issues - Skills Football Pitch Booking App

**Last Updated**: July 8, 2025  
**Current Phase**: Frontend Integration & Testing

This document tracks current issues in the codebase, prioritized by severity and impact on functionality. Each issue includes a detailed plan for resolution.

## � Medium Priority Issues

### 1. Frontend Integration Testing Required
**Status:** 🔄 IN PROGRESS  
**Impact:** Medium - Backend is complete, need to validate frontend integration  
**Files Affected:**
- `.env` file (needs new Supabase credentials)
- API integration points throughout Flutter app

**Problem:** Backend has been completely reconstructed with new Supabase instance, requiring frontend integration testing.

**Resolution Plan:**
1. Update `.env` with new Supabase URL and anon key
2. Test API connectivity and data flow
3. Validate pitch enable/disable functionality
4. Verify booking flow with new backend
5. Test real-time slot updates

**Timeline:** July 9-10, 2025

---

### 2. Performance Monitoring Setup
**Status:** ⏳ PLANNED  
**Impact:** Low - System is operational, monitoring is for optimization  
**Files Affected:** TBD

**Problem:** Need to set up monitoring for slot query performance and real-time updates.

**Resolution Plan:**
1. Implement query performance tracking
2. Set up error monitoring and alerting
3. Create performance benchmarks
4. Monitor slot generation performance under load

**Timeline:** July 15, 2025

---

## ✅ Recently Resolved Issues

### 1. ~~3-Slot Offset Bug~~ (RESOLVED ✅)
**Resolution Date:** July 8, 2025  
**Solution:** Implemented ADR-007 database-centric slot management, eliminating client-side calculations that caused the offset.

### 2. ~~Backend Infrastructure Failure~~ (RESOLVED ✅)
**Resolution Date:** July 8, 2025  
**Solution:** Complete Supabase database reconstruction with improved schema and functionality.

### 3. ~~Pitch Enable/Disable Functionality Missing~~ (RESOLVED ✅)
**Resolution Date:** July 8, 2025  
**Solution:** Added `is_enabled` field to pitch_settings with proper RLS policies and slot generation validation.

### 4. ~~Database Race Conditions~~ (RESOLVED ✅)
**Resolution Date:** July 8, 2025  
**Solution:** Implemented database constraints, triggers, and exclusion constraints to prevent race conditions.

---

## 🚫 Non-Issues (Clarifications)

### Build/Compilation Issues
**Status:** ✅ All Clear  
**Last Check:** July 8, 2025  
**Note:** All previous build and import errors have been resolved.

### Test Infrastructure
**Status:** ✅ Stable  
**Last Check:** June 2025  
**Note:** Test infrastructure is working correctly with comprehensive provider overrides.

---

## 📈 Issue Resolution History

| Month | Issues Resolved | Major Fixes |
|-------|----------------|-------------|
| July 2025 | 4 | Backend reconstruction, ADR-007 implementation |
| June 2025 | 8 | Test infrastructure, race conditions, build errors |
| May 2025 | 5 | Authentication, booking flow, UI components |

---

## 🎯 Next Review

**Scheduled:** July 15, 2025  
**Focus:** Post-integration testing issue identification  
**Goals:** Ensure production readiness

### 3. Missing Supabase Mock Setup in Widget Tests
**Status:** ✅ RESOLVED  
**Files Affected:**
- `test/features/availability/presentation/availability_screen_widget_test.dart` (Fixed)

**Problem:** Widget tests for `AvailabilityScreen` were failing with multiple issues:
- "SupabaseNotInitializedException" from date picker interactions
- Timer cleanup issues from real-time availability providers
- Riverpod provider override conflicts when rebuilding widgets
- Test expectations not matching actual UI structure

**Resolution:** ✅ Comprehensive test infrastructure overhaul completed:
- Added proper test infrastructure including `setUpAll()` and `tearDownAll()` methods with Supabase mock initialization
- Added `...testProviderOverrides` to include base Supabase mock overrides
- Added missing `userBookingsProvider` override to prevent authentication errors
- **MAJOR:** Added overrides for real-time availability providers to prevent Timer issues
- **MAJOR:** Rewrote problematic test to avoid Riverpod provider override conflicts
- Fixed provider type safety with `<Booking>[]` instead of `<dynamic>[]`
- **RESULT:** All 6 `AvailabilityScreen` widget tests now pass consistently (100% success rate)

---

### 4. Booking Model JsonKey Annotation Issues
**Status:** 🔄 PARTIALLY RESOLVED (Temporary Fix)  
**Files Affected:**
- `lib/features/booking/domain/booking_model.dart`

**Problem:** JsonKey annotations on Freezed factory constructor parameters causing build failures and preventing code generation.

**Temporary Resolution:** 🔧 Implemented manual JSON parsing in fromJson method, disabled JsonKey annotations and .g.dart generation.

**Permanent Fix Required:** 
1. Research proper JsonKey usage with Freezed factory constructors
2. Re-enable JsonSerializable code generation
3. Replace manual JSON parsing with generated methods

---

### 4. Booking Creation Race Condition and Error Handling Issues
**Status:** 🔴 ACTIVE - Multiple Critical Issues  
**Date Identified:** June 17, 2025  
**Files Affected:**
- `lib/features/booking/data/booking_repository.dart` (Edge function fallback logic)
- `lib/core/utils/logger_service.dart` (Stack trace parameter error)
- Supabase Edge Function: `create_booking` (HTTP status code issue)
- Real-time availability providers (WebSocket connection drops)

**Problem Description:**
Multiple interconnected issues during booking creation process:

1. **Edge Function HTTP Status Issue:**
   - Edge function returns status 201 (Created) but client expects different response format
   - Causes fallback to repository method even when booking succeeds
   - Log: `Edge Function failed, falling back to repository: Exception: Edge Function error: 201`

2. **False Positive Conflict Detection:**
   - Repository detects "conflicting booking" for slot that was just successfully booked
   - Booking actually succeeds in database but UI shows error
   - Log: `Found 1 conflicting booking(s) for pitch 1 at 2025-06-17T12:00:00.000`

3. **Logger Parameter Type Error:**
   - Logger service incorrectly handling StackTrace parameter
   - Log: `Invalid argument(s): Error parameter cannot take a StackTrace!`
   - Affecting error reporting and debugging capabilities

4. **Real-time Connection Instability:**
   - WebSocket connection drops during booking process
   - Log: `RealtimeSubscribeException(status: RealtimeSubscribeStatus.channelError, details: RealtimeCloseEvent(code: 1006, reason: ))`
   - Affects real-time availability updates

**Impact:**
- 🔴 **Critical:** Users see booking failure messages when bookings actually succeed
- 🔴 **Critical:** Unreliable booking confirmation feedback
- 🟡 **Medium:** Reduced error reporting quality due to logger issues
- 🟡 **Medium:** Stale availability data due to real-time connection drops

**Evidence:**
- User attempted to book slot 12:00-13:00 on June 17th, 2025
- App showed booking failure and error messages
- Database confirms booking was successfully created (ID: 89)
- Availability screen initially showed slot as available, then updated to booked

**Next Steps:**
See `docs/dev_notes/booking-creation-race-condition-analysis.md` for detailed investigation plan.

---

### 5. Date Filtering Logic Completely Broken in Availability System
**Status:** 🔴 CRITICAL - Multiple Date Filtering Failures  
**Date Identified:** June 17, 2025  
**Files Affected:**
- `lib/features/availability/application/availability_service.dart` (getBookingsForDate method)
- All availability-related providers and UI components

**Problem Description:**
The availability system has completely broken date filtering logic causing two critical issues:

1. **False Positive Bookings:**
   - Shows June 13th bookings as "booked" when viewing June 17th availability
   - Slot 14:00-15:00 shows as booked on June 17th, but actual booking is on June 13th
   - Users cannot book available slots due to phantom bookings

2. **False Negative Bookings (Security Risk):**
   - Does NOT show actual June 17th booking (12:00-13:00) as booked
   - Slot appears available when it's actually booked in database
   - Could lead to double-booking scenarios

**Evidence:**
- Database has confirmed booking for June 17th 12:00-13:00 (ID: 89)
- Availability screen shows this slot as available ❌
- Database has bookings on June 13th (14:00-15:00)
- Availability screen shows June 13th booking as booked on June 17th ❌

**Root Cause Analysis:**
Suspected timezone conversion issues in date range filtering:
- `getBookingsForDate` method uses `startOfDay` and `endOfDay` extensions
- Database stores times in UTC (+00:00)
- Client queries using local timezone
- Potential mismatch causing wrong bookings to be included/excluded

**Impact:**
- 🔴 **CRITICAL SECURITY:** Double-booking vulnerability (available slots that are actually booked)
- 🔴 **CRITICAL UX:** Users cannot book legitimately available slots
- 🔴 **DATA INTEGRITY:** Availability state doesn't match database reality

**Next Steps:**
1. Add debug logging to trace timezone conversions
2. Test date filtering with various timezone scenarios  
3. Fix date range query logic
4. Comprehensive testing across timezones

**Debugging Added:**
- Enhanced logging in `getBookingsForDate` method to track timezone conversions
- Ready for testing and analysis

---

## 🟡 High Priority Issues (Fix Before Next Release)

### 6. Test Failure in BookingConfirmationScreen
**Status:** ✅ RESOLVED  
**Files Affected:**
- `test/features/booking/presentation/booking_confirmation_screen_test.dart` (Fixed)

**Problem:** Test "calls createBooking, shows loading, then success and navigates on confirm button tap" was failing due to mismatched expectations between test and actual UX implementation.

**Resolution:** ✅ Updated test to correctly validate the actual UX flow:
- Fixed dialog text expectations ("Booking Confirmed!" vs "Booking Confirmed")
- Fixed dialog content text ("Your booking has been successfully created." vs with exclamation)
- Updated navigation expectations (2 `didPop` calls: dialog + screen vs 1)
- Test now correctly validates the multi-booking UX with success dialog

---

### 7. Flutter Deprecated API Usage
**Status:** ✅ RESOLVED  
**Files Affected:**
- `lib/features/availability/presentation/widgets/slot_list_item.dart:87,88,138,139,155` (Fixed)

**Problem:** Using deprecated `withOpacity()` method which will be removed in future Flutter versions.

**Resolution:** ✅ Replaced all 5 instances of `withOpacity()` with `withValues(alpha: x)`:
- Line 87: `Colors.grey.withOpacity(0.38)` → `Colors.grey.withValues(alpha: 0.38)`
- Line 88: `Colors.grey.withOpacity(0.12)` → `Colors.grey.withValues(alpha: 0.12)`
- Line 138: `Colors.grey.withOpacity(0.38)` → `Colors.grey.withValues(alpha: 0.38)`
- Line 139: `Colors.grey.withOpacity(0.12)` → `Colors.grey.withValues(alpha: 0.12)`
- Line 155: `Colors.black.withOpacity(0.2)` → `Colors.black.withValues(alpha: 0.2)`

---

### 8. Test Code Quality Issues
**Status:** 🔴 ACTIVE  
**Files Affected:**
- `test/features/availability/presentation/booking_limit_widget_test.dart:177,179,188`
- `test/test_helpers/supabase_mock_helper.dart:273,322`
- `test/test_helpers/supabase_mock_helper.dart:313,314` (variable naming)

**Problem:** Multiple test code quality issues:
- Using `print()` statements instead of proper logging (5 occurrences)
- Variable names using UPPER_CASE instead of lowerCamelCase (2 occurrences)

**Impact:** Code consistency, debugging clarity, lint violations.

**Resolution Plan:**
1. Replace `print()` calls with `debugPrint()` or remove if unnecessary
2. Rename UPPER_CASE variables to lowerCamelCase
3. Run `flutter analyze` to catch similar issues

---

### 9. Missing Test Dependencies
**Status:** 🔴 ACTIVE  
**Files Affected:**
- `test/test_helpers/widget_test_helper.dart:5`

**Problem:** Test file imports `shared_preferences` package which isn't a declared dependency.

**Impact:** Potential test failures, inconsistent test environment.

**Resolution Plan:**
1. Add `shared_preferences` to dev_dependencies in pubspec.yaml
2. Review all test imports for missing dependencies
3. Ensure test environment matches production dependencies

---

### 10. Legacy Deprecated API Usage (Previous)
**Status:** ✅ RESOLVED  
**Files Affected:**
- `lib/routing/app_router.dart:35` (Fixed - replaced AsyncValue.stream with .future.asStream())
- `lib/features/availability/presentation/widgets/slot_list_item.dart:84,112-114` (Fixed - replaced MaterialState with WidgetState, withOpacity with withValues)

**Problem:** Using deprecated Flutter/Riverpod APIs that will be removed in future versions.

**Resolution:** ✅ Replaced all deprecated API usage with modern equivalents.

---

## 🟢 Medium Priority Issues (Fix During Next Iteration)

### 11. Database Schema Verification Needed
**Status:** 🟡 REQUIRES VERIFICATION  
**Files Affected:**
- `docs/database_migrations/002_add_pricing_and_limits_to_pitch_settings.sql`
- Production Supabase database

**Problem:** Migration script exists for dynamic pricing and booking limits, but uncertain if applied to production database.

**Impact:** Dynamic settings may fall back to hardcoded values if schema is incomplete.

**Resolution Plan:**
1. Check current production database schema in Supabase
2. Apply migration script if pricing fields are missing
3. Verify all pitch settings have proper default values
4. Test dynamic loading of settings in application

---

### 12. Hardcoded Booking Limits in UI
**Status:** 🟡 ACTIVE  
**Files Affected:**
- `lib/features/availability/presentation/widgets/slot_list_item.dart` (hardcoded limit of 4)

**Problem:** UI logic still uses hardcoded booking limit of 4 instead of loading from dynamic settings.

**Impact:** Booking limits cannot be configured per-pitch as intended by the architecture.

**Resolution Plan:**
1. Update `SlotListItem` to use `maxBookingsPerUser` from pitch settings
2. Remove all hardcoded "4" references in booking limit logic
3. Test booking limit enforcement with different pitch configurations
4. Ensure error messages reflect actual configured limits

---

### 13. Code Quality and Linting Issues
**Status:** 🔄 IN PROGRESS  
**Files Affected:**
- `lib/features/availability/presentation/availability_screen.dart:362`
- `lib/features/availability/presentation/widgets/availability_details.dart:95`
- `lib/features/booking/data/booking_repository.dart:119`
- `lib/features/booking/presentation/my_bookings_screen.dart:4`

**Problem:** Various linting violations including missing braces, unnecessary type checks, unused imports.

**Impact:** Code maintainability, potential bugs, inconsistent style.

**Resolution Plan:**
1. Add braces to single-line if statements
2. Remove unnecessary string interpolation braces
3. Remove unused imports and unnecessary type checks
4. Run `flutter analyze` and fix all remaining issues

---

### 14. BuildContext Async Usage Warning
**Status:** 🟡 ACTIVE WARNING  
**Files Affected:**
- `lib/features/home/<USER>/home_page.dart:25`

**Problem:** Using BuildContext across async gaps without mounted checks.

**Impact:** Potential runtime errors if widget is disposed during async operations.

**Resolution Plan:**
1. Add `mounted` checks before using BuildContext after async operations
2. Review all async functions that use BuildContext
3. Implement proper error handling patterns

---

### 15. Print Statement Usage (Code Quality) - Legacy
**Status:** ✅ RESOLVED  
**Files Affected:**
- `lib/features/booking/presentation/booking_confirmation_screen.dart:159` (Fixed)

**Problem:** Using print() statements instead of proper logging framework.

**Resolution:** ✅ Replaced print with debugPrint and added TODO for logging service migration.

---

## Resolution Timeline

### Sprint 1 (Current - Week of June 12, 2025)
- [x] Fix duplicate `userBookingsProvider` (Critical #1)
- [x] Add missing route constants (Critical #2)
- [x] Replace deprecated APIs (High Priority #5)
- [ ] Fix code quality issues (High Priority #6)

### Sprint 2 (Week of June 19, 2025)
- [ ] Add BuildContext mounted checks (Medium Priority #7)
- [ ] Improve test infrastructure (Low Priority #9)
- [ ] Complete logging migration (Low Priority #10)

### Sprint 3 (Week of June 26, 2025)
- [ ] Research and fix JsonKey annotation issues (Critical #4)
- [ ] Comprehensive testing of all fixes

---

## Monitoring and Prevention

1. **Pre-commit Hooks:** Set up `flutter analyze` and basic linting checks
2. **CI/CD Pipeline:** Add automated testing for duplicate providers and missing constants
3. **Code Review Checklist:** Include checks for deprecated API usage and proper error handling
4. **Documentation:** Maintain this issues list and update as new issues are discovered

---

*Last updated: June 12, 2025*
