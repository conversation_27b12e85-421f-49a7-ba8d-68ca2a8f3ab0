# Business Logic Implementation Matrix

**Quick Reference**: Where each business rule is implemented across the codebase

## Time-Based Filtering Rules

| Business Rule | Implementation Location | Logic | Issues |
|---------------|------------------------|-------|---------|
| **"Upcoming" Booking Classification** | `lib/features/booking/presentation/my_bookings_screen.dart:43` | `slotStartTime.isAfter(now)` | ✅ Correct |
| **"Past" Booking Classification** | `lib/features/booking/presentation/my_bookings_screen.dart:44` | `slotStartTime.isBefore(now)` | ⚠️ Missing `slotStartTime == now` case |
| **Booking Limit Count (Frontend)** | `lib/features/availability/presentation/widgets/slot_list_item.dart:80-90` | `slotStartTime.isAfter(DateTime.now())` | ⚠️ Duplicate logic |
| **Booking Limit Count (Backend)** | `supabase/functions/create_booking_atomic/index.ts:95` | `slot_end_time > now` | 🔴 **Inconsistent with frontend** |
| **Past Slot Filtering (Data Layer)** | `lib/features/availability/application/database_slot_provider.dart:54-64` | `startTime.isAfter(now)` for today only | ✅ Correct |
| **Booking Creation Validation** | `supabase/functions/create_booking_atomic/index.ts:64-76` | `startTime >= (now - 5min)` | ✅ Correct (5min grace) |

## Date Range Filtering Rules

| Business Rule | Implementation Location | Logic | Issues |
|---------------|------------------------|-------|---------|
| **Daily Booking Query** | `lib/features/availability/application/availability_service.dart:67-75` | `date.startOfDay` to `date.endOfDay` | 🔴 **Timezone issues documented** |
| **Slot Generation Date Validation** | `supabase/migrations/20250618_create_time_slots_schema.sql:121-125` | `p_date >= CURRENT_DATE` | ✅ Correct |

## Booking Status Rules

| Business Rule | Implementation Location | Logic | Issues |
|---------------|------------------------|-------|---------|
| **Active Status for Limits** | `lib/features/availability/presentation/widgets/slot_list_item.dart:85-88` | Excludes cancelled statuses | ✅ Correct |
| **Active Status for Backend Count** | `supabase/functions/create_booking_atomic/index.ts:93-94` | `['confirmed', 'pending_payment']` | ✅ Consistent |

## Booking Limit Enforcement

| Business Rule | Implementation Location | Logic | Issues |
|---------------|------------------------|-------|---------|
| **Max Bookings Check (UI)** | `lib/features/availability/presentation/widgets/slot_list_item.dart:96` | `upcomingBookingsCount >= maxBookings` | ⚠️ Uses frontend count logic |
| **Max Bookings Check (Backend)** | `supabase/functions/create_booking_atomic/index.ts:123-133` | `userBookings.length >= max_bookings_per_user` | ⚠️ Uses backend count logic |
| **Booking Limit Configuration** | `lib/features/availability/domain/pitch_settings_model.dart:11` | `maxBookingsPerUser` field | ✅ Dynamic from DB |

## Key Inconsistencies Identified

### 🔴 Critical: Booking Count Logic Mismatch
```
Frontend: COUNT(bookings WHERE slotStartTime > now AND status = active)
Backend:  COUNT(bookings WHERE slotEndTime > now AND status = active)
```
**Files**: 
- `lib/features/availability/presentation/widgets/slot_list_item.dart:82-90`
- `supabase/functions/create_booking_atomic/index.ts:95`

### 🔴 Critical: Timezone Date Filtering
```
Problem: date.startOfDay/endOfDay in local time vs UTC database storage
```
**Files**:
- `lib/features/availability/application/availability_service.dart:67-75`
- Documented in `docs/known_issues.md:185-220`

### ⚠️ Medium: Edge Case Handling
```
Missing: Bookings where slotStartTime == now (falls through filters)
```
**Files**:
- `lib/features/booking/presentation/my_bookings_screen.dart:43-44`

## Migration Priority Matrix

| Priority | Issue | Impact | Effort | Files to Change |
|----------|-------|---------|---------|----------------|
| **P0** | Fix upcoming booking discrepancy | User-facing bug | Low | `my_bookings_screen.dart` |
| **P0** | Booking count logic consistency | Security/UX | Medium | `slot_list_item.dart`, `create_booking_atomic/index.ts` |
| **P1** | Timezone date filtering | Data integrity | High | `availability_service.dart`, multiple providers |
| **P2** | Centralize business rules | Technical debt | High | Multiple files |
| **P3** | Edge case handling | Minor UX | Low | `my_bookings_screen.dart` |

## Quick Fix for Immediate Issue

To fix the reported discrepancy where user sees 3 upcoming bookings instead of 2:

**Problem**: 09:00-10:00 slot shows as "upcoming" but current time is 10:19 AM

**Root Cause**: Frontend logic `slotStartTime.isAfter(now)` is working correctly. The issue is likely:
1. Data coming from backend has wrong timestamps (timezone issue), OR
2. Different business logic being applied somewhere else in the flow

**Immediate Investigation**:
1. Check actual booking data timestamps in the My Bookings screen
2. Verify timezone handling in booking fetch logic
3. Compare with availability screen filtering logic

**Quick Fix** (while migration plan is implemented):
```dart
// In my_bookings_screen.dart, add logging to debug the issue
final now = DateTime.now();
logger.d('Current time: $now');
for (final booking in bookings) {
  logger.d('Booking ${booking.id}: ${booking.slotStartTime} - isAfter($now): ${booking.slotStartTime.isAfter(now)}');
}
```

This will help identify if the problem is in data timestamps or the filtering logic itself.
