# ADR-006 Database Migration Application Guide

## 🚨 CRITICAL: Database Foundation Setup

### Prerequisites
1. Access to your Supabase project dashboard
2. SQL Editor permissions in Supabase
3. Current database backup (optional but recommended)

### Step 1: Verify Current Schema
1. Go to Supabase Dashboard → SQL Editor
2. Run the verification script: `scripts/verify_database_schema.sql`
3. Note which fields/constraints are missing

### Step 2: Apply Migrations (REQUIRED)

#### Migration 1: Add Pricing and Limits
**File:** `docs/database_migrations/002_add_pricing_and_limits_to_pitch_settings.sql`

**What it does:**
- Adds `price_per_hour` field to pitch_settings
- Adds `max_bookings_per_user` field  
- Sets default values (5000 MWK per hour, 4 booking limit)
- Adds validation constraints

**Status:** ❌ NOT APPLIED YET

#### Migration 2: Add Race Condition Prevention  
**File:** `docs/database_migrations/003_add_race_condition_prevention.sql`

**What it does:**
- Creates unique constraint to prevent double bookings
- Adds optimized indexes for real-time queries
- Creates atomic booking validation function
- Prevents overlapping bookings at database level

**Status:** ❌ NOT APPLIED YET

### Step 3: Verification After Migration
Run verification script again to confirm:
- ✅ price_per_hour and max_bookings_per_user fields exist
- ✅ Unique constraint `unique_active_booking_slots` exists
- ✅ Function `validate_booking_slot` exists
- ✅ All indexes are created

### Step 4: Test Data Integrity
After applying migrations:
1. Verify existing bookings are not affected
2. Test that new pricing fields have correct default values
3. Ensure unique constraint works (try creating duplicate booking)

## 🔥 IMMEDIATE NEXT ACTIONS

1. **FIRST:** Run `scripts/verify_database_schema.sql` in Supabase SQL Editor
2. **THEN:** Apply migration 002 in SQL Editor
3. **THEN:** Apply migration 003 in SQL Editor  
4. **FINALLY:** Run verification script again to confirm success

## ⚠️ Important Notes

- These migrations are **BACKWARD COMPATIBLE** - existing data will not be lost
- Default values ensure existing pitch settings continue working
- The unique constraint only prevents NEW double bookings (doesn't affect existing data)
- All migrations use `IF NOT EXISTS` to be safely re-runnable

## 🎯 Success Criteria

After applying migrations, you should have:
- Dynamic pricing fields populated in pitch_settings
- Race condition prevention at database level
- Ready foundation for real-time provider integration
- 100% ADR-006 Phase 1 completion

---

**Next Session Focus:** After database migrations, we'll connect the real-time providers to the UI and implement optimistic booking flow.
