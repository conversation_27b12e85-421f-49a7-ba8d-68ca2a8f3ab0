# Business Logic Audit and Migration Plan

**Date**: July 9, 2025
**Context**: User reports discrepancy - sees 3 upcoming bookings but should only see 2 (09:00-10:00 slot is in the past)

## Executive Summary

This audit reveals a significant architectural inconsistency: business rules for booking filtering and time-based logic are scattered between frontend UI components and backend data providers, leading to inconsistent behavior and the reported discrepancy.

**Key Finding**: The "upcoming bookings" filter in My Bookings screen uses `slotStartTime.isAfter(now)` logic in the frontend, but availability screen filtering and booking limit checks are handled at different layers with different implementations.

## 1. Current Business Logic Distribution

### 1.1 Frontend Business Rules (UI Layer)

#### **Booking Categorization Logic** 
**Location**: `lib/features/booking/presentation/my_bookings_screen.dart:43-46`
```dart
final upcoming = bookings.where((b) => b.slotStartTime.isAfter(now)).toList();
final past = bookings.where((b) => b.slotStartTime.isBefore(now)).toList();
```
**Business Rule**: "Upcoming" = `slotStartTime > currentTime`, "Past" = `slotStartTime < currentTime`
**Issue**: This correctly filters but leaves gaps for slots where `slotStartTime == now` (edge case)

#### **Booking Limit Display Logic**
**Location**: `lib/features/availability/presentation/widgets/slot_list_item.dart:80-92`
```dart
final upcomingBookingsCount = bookings
    .where((booking) =>
        booking.slotStartTime.isAfter(DateTime.now()) &&
        booking.status != BookingStatus.cancelledByUser &&
        booking.status != BookingStatus.cancelledByAdmin)
    .length;
```
**Business Rule**: Count only future bookings with active status for booking limit enforcement
**Issue**: Duplicate time filtering logic vs. My Bookings screen

### 1.2 Data Provider Business Rules

#### **Past Slot Filtering** 
**Location**: `lib/features/availability/application/database_slot_provider.dart:54-64`
```dart
if (selectedDate.isAtSameMomentAs(today)) {
  // For today, filter out slots that have already started
  filteredSlots = allSlots.where((slot) {
    return slot.startTime.isAfter(now);
  }).toList();
} else {
  // For future dates, show all slots
  filteredSlots = allSlots;
}
```
**Business Rule**: For today only, filter out slots where `startTime <= now`; future dates show all slots
**Consistency**: Aligns with backend business logic

#### **Date Range Filtering**
**Location**: `lib/features/availability/application/availability_service.dart:67-75`
```dart
final DateTime startDate = date.startOfDay;
final DateTime endDate = date.endOfDay;
// Query bookings between startDate and endDate
```
**Business Rule**: Fetch bookings for a specific calendar date (00:00:00 to 23:59:59)
**Issue**: Known timezone handling problems documented in `docs/known_issues.md`

### 1.3 Backend Business Rules

#### **Time-Based Booking Validation**
**Location**: `supabase/functions/create_booking_atomic/index.ts:64-76`
```typescript
const now = new Date()
const gracePeriod = 5 * 60 * 1000 // 5 minutes in milliseconds

if (startTime.getTime() < (now.getTime() - gracePeriod)) {
  return new Response(JSON.stringify({ 
    error: 'PAST_BOOKING', 
    message: 'Cannot book slots in the past' 
  }), { status: 400 })
}
```
**Business Rule**: Cannot book slots starting more than 5 minutes in the past
**Purpose**: Prevents past bookings with grace period for UI latency

#### **Active Booking Count Logic**
**Location**: `supabase/functions/create_booking_atomic/index.ts:86-95`
```typescript
.gt('slot_end_time', now.toISOString()) // Only count future/active bookings
```
**Business Rule**: Count bookings as "active" if `slotEndTime > now` (not `slotStartTime > now`)
**Discrepancy**: Different from frontend logic which uses `slotStartTime > now`

#### **Database Constraints**
**Location**: `supabase/migrations/20250618_create_time_slots_schema.sql:33-37`
```sql
CONSTRAINT valid_time_range CHECK (end_time > start_time),
CONSTRAINT valid_date CHECK (slot_date >= CURRENT_DATE - INTERVAL '1 day'),
CONSTRAINT booking_consistency CHECK (...)
```
**Business Rules**: 
- Slots must have valid time ranges
- Slots cannot be created for past dates (except today)
- Booking status must be consistent with booking_id

## 2. Identified Inconsistencies and Issues

### 2.1 Critical Issues

#### **Issue #1: Booking Count Logic Mismatch**
- **Frontend**: Counts bookings where `slotStartTime > now`
- **Backend**: Counts bookings where `slotEndTime > now`
- **Impact**: Booking limits may be calculated differently between UI display and actual enforcement

#### **Issue #2: Timezone Handling in Date Filtering**
**Location**: `docs/known_issues.md:185-220`
- **Problem**: Date range filtering breaks across timezone boundaries
- **Evidence**: Shows wrong bookings for dates (June 13 bookings appear on June 17)
- **Security Risk**: Could allow double-booking of actually reserved slots

#### **Issue #3: Edge Case Handling**
- **Missing Logic**: Bookings exactly at current time (`slotStartTime == now`)
- **Frontend**: Not handled consistently (falls through filters)
- **Impact**: These bookings disappear from both "upcoming" and "past" lists

### 2.2 Minor Issues

#### **Issue #4: Duplicate Logic**
- Time filtering logic implemented 3+ times across different components
- Each implementation has subtle differences in edge case handling
- Maintenance burden and inconsistency risk

#### **Issue #5: Business Rule Documentation**
- No centralized documentation of business rules
- Rules scattered across code comments and ADRs
- Difficult to verify correctness and consistency

## 3. Root Cause Analysis

### 3.1 Architectural Problems

1. **Lack of Domain Layer**: Business rules embedded in UI and data layers rather than centralized domain services
2. **Inconsistent Abstractions**: Time-based logic handled at multiple levels with different implementations  
3. **Missing Business Rule Tests**: No comprehensive test suite verifying business rule consistency across layers

### 3.2 Technical Debt

1. **Legacy Code**: Different implementations evolved over time without harmonization
2. **Timezone Complexity**: DateTime handling scattered across components without standardized approach
3. **Provider Proliferation**: Multiple providers handling similar logic with subtle differences

## 4. Migration Plan

### 4.1 Phase 1: Centralize Business Rules (Week 1)

#### **Create Domain Services**
```dart
// lib/features/booking/domain/booking_business_rules.dart
class BookingBusinessRules {
  /// Determines if a booking should be considered "upcoming"
  static bool isUpcomingBooking(Booking booking, DateTime currentTime) {
    // Centralized logic: booking is upcoming if it starts after now
    return booking.slotStartTime.isAfter(currentTime);
  }
  
  /// Determines if a booking should count toward active booking limit
  static bool countsTowardBookingLimit(Booking booking, DateTime currentTime) {
    // Use end time for booking limits (aligns with backend)
    return booking.slotEndTime.isAfter(currentTime) && 
           !booking.isCancelled;
  }
  
  /// Determines if a slot can be booked based on time
  static bool isSlotBookable(DateTime slotStartTime, DateTime currentTime) {
    // 5-minute grace period aligns with backend
    const gracePeriod = Duration(minutes: 5);
    return slotStartTime.isAfter(currentTime.subtract(gracePeriod));
  }
}
```

#### **Create Time Service**
```dart
// lib/core/services/time_service.dart
class TimeService {
  /// Get current time (allows mocking in tests)
  DateTime now() => DateTime.now();
  
  /// Standardized date range for booking queries
  DateTimeRange dateRangeForDate(DateTime date) {
    return DateTimeRange(
      start: date.startOfDay.toUtc(),
      end: date.endOfDay.toUtc(),
    );
  }
}
```

### 4.2 Phase 2: Migrate Frontend Logic (Week 2)

#### **Update My Bookings Screen**
```dart
// Replace direct filtering with domain service
final upcoming = bookings.where((b) => 
  BookingBusinessRules.isUpcomingBooking(b, timeService.now())
).toList();
```

#### **Update Booking Limit Logic**
```dart
// Use consistent counting logic
final activeBookingsCount = bookings.where((b) =>
  BookingBusinessRules.countsTowardBookingLimit(b, timeService.now())
).length;
```

#### **Standardize Slot Filtering**
```dart
// Replace ad-hoc filtering with domain service
final bookableSlots = allSlots.where((slot) =>
  BookingBusinessRules.isSlotBookable(slot.startTime, timeService.now())
).toList();
```

### 4.3 Phase 3: Backend Alignment (Week 3)

#### **Update Edge Function Logic**
Ensure backend business rules match domain service implementations:
```typescript
// Align with BookingBusinessRules.countsTowardBookingLimit
.gt('slot_end_time', now.toISOString())
```

#### **Add Database Functions**
```sql
-- Create database functions that match domain logic
CREATE OR REPLACE FUNCTION is_booking_upcoming(
  slot_start_time TIMESTAMPTZ,
  current_time TIMESTAMPTZ DEFAULT NOW()
) RETURNS BOOLEAN AS $$
BEGIN
  RETURN slot_start_time > current_time;
END;
$$ LANGUAGE plpgsql IMMUTABLE;
```

### 4.4 Phase 4: Fix Timezone Issues (Week 4)

#### **Standardize DateTime Handling**
1. **All database queries use UTC timestamps**
2. **Frontend converts to UTC before sending to backend**
3. **UI displays in local time only for presentation**

#### **Update Date Range Logic**
```dart
// Fix timezone-aware date filtering
final queryRange = timeService.dateRangeForDate(selectedDate);
final bookings = await repository.getBookingsInRange(
  pitchId: pitchId,
  startTime: queryRange.start,
  endTime: queryRange.end,
);
```

## 5. Implementation Strategy

### 5.1 Backward Compatibility

1. **Parallel Implementation**: Keep existing logic while implementing new domain services
2. **Feature Flags**: Use feature flags to gradually migrate components
3. **Comprehensive Testing**: Ensure identical behavior during migration

### 5.2 Testing Strategy

#### **Business Rule Tests**
```dart
group('BookingBusinessRules', () {
  test('isUpcomingBooking handles edge cases correctly', () {
    final currentTime = DateTime(2025, 7, 9, 10, 19);
    
    // Booking starting exactly at current time
    final exactTimeBooking = createBooking(startTime: currentTime);
    expect(BookingBusinessRules.isUpcomingBooking(exactTimeBooking, currentTime), false);
    
    // Booking starting 1 minute in future
    final futureBooking = createBooking(startTime: currentTime.add(Duration(minutes: 1)));
    expect(BookingBusinessRules.isUpcomingBooking(futureBooking, currentTime), true);
  });
});
```

#### **Integration Tests**
- Cross-layer consistency tests
- Timezone boundary tests  
- Edge case scenario tests

### 5.3 Rollout Plan

#### **Week 1: Foundation**
- [ ] Create `BookingBusinessRules` domain service
- [ ] Create `TimeService` utility
- [ ] Add comprehensive unit tests
- [ ] Update documentation

#### **Week 2: Frontend Migration**
- [ ] Migrate My Bookings screen filtering
- [ ] Migrate booking limit logic in SlotListItem
- [ ] Migrate availability screen filtering
- [ ] Add integration tests

#### **Week 3: Backend Alignment**
- [ ] Update Edge Function logic
- [ ] Create database business rule functions
- [ ] Update SQL queries for consistency
- [ ] Performance testing

#### **Week 4: Timezone Fix**
- [ ] Implement timezone-aware date handling
- [ ] Fix date range query logic
- [ ] Update all DateTime serialization/deserialization
- [ ] End-to-end testing across timezones

## 6. Success Metrics

### 6.1 Functional Correctness
- [ ] User sees exactly 2 upcoming bookings (not 3) in the reported scenario
- [ ] Booking limits calculated consistently across all UI components
- [ ] Date filtering works correctly across timezone boundaries
- [ ] Edge cases (bookings at exact current time) handled consistently

### 6.2 Code Quality
- [ ] Business rules centralized in domain services
- [ ] Duplicate logic eliminated
- [ ] 100% test coverage for business rule logic
- [ ] Comprehensive documentation of all business rules

### 6.3 Performance
- [ ] No performance regression from centralization
- [ ] Reduced data fetching due to more efficient filtering
- [ ] Improved caching effectiveness

## 7. Long-term Architecture Vision

### 7.1 Business Rule Centralization
```
┌─────────────────────────┐
│     Presentation        │
│   (UI Components)       │
└─────────┬───────────────┘
          │ delegates to
┌─────────▼───────────────┐
│   Domain Services       │
│ (BookingBusinessRules)  │
│ (TimeService)          │
└─────────┬───────────────┘
          │ uses
┌─────────▼───────────────┐
│   Data Layer           │
│ (Repositories)         │
└─────────┬───────────────┘
          │ queries
┌─────────▼───────────────┐
│     Database           │
│ (Business Constraints) │
└─────────────────────────┘
```

### 7.2 Future Enhancements
1. **Configuration-Driven Rules**: Make business rules configurable per pitch
2. **Audit Trail**: Track business rule changes and their impact
3. **A/B Testing**: Enable testing different business rule implementations
4. **Real-time Rule Updates**: Allow updating business rules without app deployment

## 8. Risk Mitigation

### 8.1 Identified Risks
- **Behavior Changes**: Users might notice different filtering behavior during migration
- **Performance Impact**: Centralized logic might be slower than inline implementations
- **Regression Risk**: Complex migration might introduce new bugs

### 8.2 Mitigation Strategies
- **Gradual Rollout**: Migrate one component at a time with thorough testing
- **Feature Flags**: Ability to roll back quickly if issues are discovered
- **Monitoring**: Add metrics to track behavior changes and performance impact
- **User Communication**: Notify users of improved accuracy in booking displays

## Conclusion

The current business logic distribution creates maintenance challenges and user-facing bugs like the reported discrepancy. By centralizing business rules in domain services and ensuring consistency between frontend and backend implementations, we can:

1. **Fix the immediate issue**: Correct upcoming/past booking categorization
2. **Improve maintainability**: Single source of truth for business rules  
3. **Enhance reliability**: Consistent behavior across all components
4. **Enable scalability**: Easy to modify rules as business requirements evolve

The migration plan provides a safe, incremental path to achieving this improved architecture while maintaining system stability and user experience.
