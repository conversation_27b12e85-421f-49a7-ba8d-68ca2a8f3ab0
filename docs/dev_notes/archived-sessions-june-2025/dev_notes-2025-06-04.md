# Development Notes - Skills Football Pitch Booking App

## 2025-06-04 - Multi-Booking Cart System Planning Session

### **Current State Summary**
- ✅ Basic single booking flow operational with custom exception handling
- ✅ 4-booking limit enforced (hardcoded) 
- ✅ Custom exceptions: `SlotUnavailableException`, `BookingLimitReachedException`, `GeneralBookingFailureException`
- ✅ Enhanced UI error handling with color-coded feedback
- ✅ Clean architecture with Riverpod state management
- ⚠️ **Hardcoded values**: booking limits, pricing (currently free), slot durations

### **Major Architectural Decision Point: Multi-Booking Cart System**

**Context**: User requirements indicate:
- Pricing is essential (not free forever)
- Users will book multiple slots in single sessions
- Admin interface needed for dynamic pitch configuration
- Settings like slot duration, max bookings, pricing must be configurable

**Problem**: Current single-booking confirmation flow doesn't scale for:
- Multiple slot selection
- Pricing calculations
- Dynamic configuration from admin interface
- Shopping cart UX patterns users expect

### **Proposed Solution: Phased Implementation**

#### **Phase 1: Foundation Layer (This Week)**
**Goal**: Create foundation without breaking existing functionality

**Components to Build**:
1. **Dynamic Pitch Settings Model** (`PitchSettings`)
   - Replaces hardcoded values with database-driven configuration
   - Includes: pricing, slot duration, booking limits, operating hours
   - Real-time updates capability for admin changes

2. **Enhanced Data Layer**
   - `PitchSettingsRepository` with Supabase integration
   - Database schema: `pitch_settings` table
   - Riverpod providers for settings management

3. **Booking Item Model** (`BookingItem`)
   - Represents potential bookings before confirmation
   - Includes calculated pricing and duration
   - Replaces current ad-hoc parameter passing

**Risk Assessment**:
- ⚠️ **Complexity Risk**: Adding many new models/providers
- ⚠️ **Migration Risk**: Changing existing booking flow
- ✅ **Mitigation**: Maintain backward compatibility during transition

#### **Phase 2: Cart Implementation (Next Week)**
**Goal**: Transform confirmation screen into cart system

**Components**:
1. **Shopping Cart State Management**
   - `BookingCartNotifier` for cart operations
   - Conflict detection at cart level
   - Dynamic limit enforcement per pitch

2. **Enhanced UI Flow**
   - "Add to Cart" buttons in availability screen
   - Cart review screen with pricing breakdown
   - Batch booking confirmation

#### **Phase 3: Integration & Polish (Following Week)**
**Goal**: Connect all pieces and optimize

**Components**:
1. Real-time settings updates
2. Payment integration preparation
3. Performance optimization
4. Comprehensive testing

### **Key Concerns Raised** 🚨
1. **Application Bloat**: Adding significant complexity for cart system
2. **Over-engineering**: May be excessive for current user base  
3. **Maintenance Burden**: More moving parts to maintain
4. **Development Time**: Complex system takes away from core functionality
5. **User Confusion**: Cart UI may overcomplicate simple booking flow

### **REVISED DECISION: Minimal Viable Enhancement**

After careful consideration of complexity vs. value, we're choosing a **much simpler approach**:

#### **Approach: Enhanced Single Booking with Dynamic Settings**
**Philosophy**: Solve the actual problems (hardcoded values, pricing) without adding cart complexity

**Implementation**:
1. **Add Basic Dynamic Settings** (1-2 days)
   - Simple `PitchSettings` model with just: price_per_hour, slot_duration, max_bookings
   - Single database table, minimal Riverpod provider
   - Replace hardcoded values in existing booking flow

2. **Enhance Current Confirmation Screen** (1 day)
   - Add pricing display to existing `BookingConfirmationScreen`
   - Show calculated cost based on dynamic settings
   - Keep single-booking flow intact

3. **Multi-Booking: Add "Book Another" Option** (1 day)
   - After successful booking, show "Book Another Slot" button
   - Simple navigation back to availability with success feedback
   - No cart complexity, just better UX flow

**Benefits of This Approach**:
- ✅ Solves core problems (pricing, dynamic config) in ~3 days
- ✅ Minimal code additions to existing architecture
- ✅ Easy to maintain and understand
- ✅ Can evolve to cart later if actually needed
- ✅ Delivers immediate value

**What We're NOT Building** (for now):
- ❌ Complex cart state management
- ❌ Multiple Riverpod providers for cart operations
- ❌ Cart UI components and navigation
- ❌ Batch booking conflict detection
- ❌ Shopping cart-style pricing breakdown

### **Next Steps** (Immediate)
1. **Today**: Update ADR-005 with simplified approach
2. **Tomorrow**: Implement basic PitchSettings model and database schema
3. **Day 3**: Replace hardcoded values with dynamic settings
4. **Day 4**: Add pricing display to confirmation screen
5. **Day 5**: Add "Book Another" flow enhancement

**Future Decisions**: If users actually request cart functionality after using the enhanced single booking flow, we can implement it then. For now, we solve the real problems (pricing, configuration) without architectural complexity.

---

## Previous Development Sessions

*(Future sessions will be logged here chronologically)*