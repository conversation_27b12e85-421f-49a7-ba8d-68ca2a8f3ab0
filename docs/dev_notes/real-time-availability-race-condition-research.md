# Real-Time Availability & Race Condition Research
**Date:** June 12, 2025  
**Purpose:** Research findings for implementing real-time availability updates and race condition handling in the football pitch booking system

## 📋 Table of Contents
1. [Executive Summary](#executive-summary)
2. [Supabase Realtime Capabilities](#supabase-realtime-capabilities)
3. [Database Race Condition Prevention](#database-race-condition-prevention)
4. [Flutter Riverpod Real-Time Patterns](#flutter-riverpod-real-time-patterns)
5. [Implementation Architecture](#implementation-architecture)
6. [Performance Considerations](#performance-considerations)
7. [Error Handling Strategies](#error-handling-strategies)
8. [Testing Approaches](#testing-approaches)
9. [Best Practices & Recommendations](#best-practices--recommendations)
10. [Implementation Roadmap](#implementation-roadmap)

## 🎯 Executive Summary

**Key Findings:**
- Supabase Realtime provides robust WebSocket-based real-time updates with built-in RLS support
- PostgreSQL unique constraints and atomic transactions effectively prevent race conditions
- Flutter Riverpod has excellent patterns for optimistic UI updates and real-time data management
- Database-level concurrency control is more reliable than client-side solutions
- Proper indexing and RLS policy optimization are crucial for performance at scale

**Recommended Approach:**
1. **Database Layer:** Unique constraints + atomic Edge Functions for booking creation
2. **Real-Time Layer:** Supabase Realtime subscriptions filtered by date/pitch
3. **Client Layer:** Riverpod providers with optimistic updates and automatic invalidation
4. **Error Handling:** Specific exception types with clear user feedback and recovery options

---

## 🔄 Supabase Realtime Capabilities

### Core Features
- **WebSocket-based real-time updates** with automatic reconnection
- **Row Level Security (RLS) integration** for secure real-time data
- **Targeted subscriptions** with filters to optimize performance
- **Broadcast and Presence** capabilities for additional real-time features

### Real-Time Subscription Patterns
```dart
// Basic table subscription with filters
final channel = supabase.channel('bookings_channel');
channel.onPostgresChanges(
  event: PostgresChangeEvent.all,
  schema: 'public',
  table: 'bookings',
  filter: PostgresChangeFilter(
    type: PostgresChangeFilterType.eq,
    column: 'pitch_id',
    value: pitchId,
  ),
  callback: (payload) {
    // Handle real-time booking changes
  },
).subscribe();

// Stream-based approach (recommended)
final stream = supabase
  .from('bookings')
  .stream(primaryKey: ['id'])
  .eq('pitch_id', pitchId)
  .gte('slot_start_time', selectedDate.startOfDay)
  .lt('slot_start_time', selectedDate.endOfDay);
```

### Connection Management
- **Automatic reconnection** on network failures
- **Subscription lifecycle management** with proper cleanup
- **Error handling** for connection drops and auth failures

### Performance Optimizations
- **Filtered subscriptions** reduce unnecessary updates
- **Policy caching** for the duration of JWT tokens
- **Geographically distributed** servers for low latency

---

## 🔒 Database Race Condition Prevention

### Atomic Booking Creation Strategy

#### 1. Database Constraints
```sql
-- Prevent overlapping bookings at database level
CREATE UNIQUE INDEX unique_active_booking_slots 
ON bookings (pitch_id, slot_start_time, slot_end_time) 
WHERE status IN ('confirmed', 'pending_payment');

-- Ensure valid time ranges
ALTER TABLE bookings 
ADD CONSTRAINT valid_time_range 
CHECK (slot_end_time > slot_start_time);

-- Prevent past bookings
ALTER TABLE bookings 
ADD CONSTRAINT no_past_bookings 
CHECK (slot_start_time > NOW() - INTERVAL '5 minutes');
```

#### 2. Atomic Edge Function
```typescript
// Supabase Edge Function: create_booking_atomic
export default async function handler(req: Request) {
  const { pitch_id, slot_start_time, slot_end_time, user_id } = await req.json();
  
  // Use database transaction for atomicity
  const { data, error } = await supabase.rpc('create_booking_atomic', {
    p_pitch_id: pitch_id,
    p_start_time: slot_start_time,
    p_end_time: slot_end_time,
    p_user_id: user_id
  });
  
  if (error) {
    if (error.code === '23505') { // Unique constraint violation
      return new Response(
        JSON.stringify({ 
          error: 'SLOT_UNAVAILABLE', 
          message: 'This slot was just booked by another user' 
        }),
        { status: 409 }
      );
    }
    // Handle other errors...
  }
  
  return new Response(
    JSON.stringify({ success: true, booking_id: data }),
    { status: 200 }
  );
}
```

#### 3. Database Function with Validation
```sql
CREATE OR REPLACE FUNCTION create_booking_atomic(
  p_pitch_id INTEGER,
  p_start_time TIMESTAMPTZ,
  p_end_time TIMESTAMPTZ,
  p_user_id UUID
) RETURNS UUID AS $$
DECLARE
  v_booking_id UUID;
  existing_count INTEGER;
BEGIN
  -- Check for overlapping bookings atomically
  SELECT COUNT(*) INTO existing_count
  FROM bookings 
  WHERE pitch_id = p_pitch_id 
    AND status IN ('confirmed', 'pending_payment')
    AND (
      (slot_start_time <= p_start_time AND slot_end_time > p_start_time) OR
      (slot_start_time < p_end_time AND slot_end_time >= p_end_time) OR  
      (slot_start_time >= p_start_time AND slot_end_time <= p_end_time)
    );
    
  IF existing_count > 0 THEN
    RAISE EXCEPTION 'SLOT_UNAVAILABLE: Slot already booked';
  END IF;
  
  -- Insert new booking
  INSERT INTO bookings (user_id, pitch_id, slot_start_time, slot_end_time)
  VALUES (p_user_id, p_pitch_id, p_start_time, p_end_time)
  RETURNING id INTO v_booking_id;
  
  RETURN v_booking_id;
END;
$$ LANGUAGE plpgsql;
```

### Concurrency Control Mechanisms

#### PostgreSQL Isolation Levels
- **Read Committed** (default): Good for most booking operations
- **Repeatable Read**: For complex multi-step operations
- **Serializable**: Maximum isolation but with performance overhead

#### Advisory Locks for Complex Operations
```sql
-- Use advisory locks for critical sections
SELECT pg_advisory_lock(pitch_id, extract(epoch from slot_start_time)::bigint);
-- Perform complex booking logic
SELECT pg_advisory_unlock(pitch_id, extract(epoch from slot_start_time)::bigint);
```

---

## ⚡ Flutter Riverpod Real-Time Patterns

### Optimistic UI Updates
```dart
@riverpod
class BookingCreation extends _$BookingCreation {
  @override
  AsyncValue<void> build() => const AsyncValue.data(null);
  
  Future<void> createBooking({
    required int pitchId,
    required DateTime startTime,
    required DateTime endTime,
  }) async {
    // Optimistic update - immediately show loading
    state = const AsyncValue.loading();
    
    try {
      // Call atomic booking function
      final response = await ref.read(supabaseClientProvider)
        .functions
        .invoke('create_booking_atomic', body: {
          'pitch_id': pitchId,
          'slot_start_time': startTime.toIso8601String(),
          'slot_end_time': endTime.toIso8601String(),
        });
        
      if (response.status != 200) {
        final error = response.data['error'];
        throw BookingException(error, response.data['message']);
      }
      
      // Success - invalidate related providers
      ref.invalidate(availableSlotsProvider);
      ref.invalidate(userBookingsProvider);
      
      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }
}
```

### Real-Time Data Providers
```dart
@riverpod
Stream<List<TimeSlotInfo>> realTimeAvailableSlots(
  RealTimeAvailableSlotsRef ref,
  DateTime selectedDate,
  int pitchId,
) async* {
  final supabase = ref.watch(supabaseClientProvider);
  
  // Combine initial data load with real-time updates
  yield* supabase
    .from('bookings')
    .stream(primaryKey: ['id'])
    .eq('pitch_id', pitchId)
    .gte('slot_start_time', selectedDate.startOfDay.toIso8601String())
    .lt('slot_start_time', selectedDate.endOfDay.toIso8601String())
    .map((bookings) => _calculateAvailableSlots(bookings, selectedDate))
    .handleError((error) {
      // Log error and provide fallback
      ref.read(errorLoggerProvider).logError(error);
    });
}

@riverpod
class TimeSensitiveSlots extends _$TimeSensitiveSlots {
  Timer? _timer;
  
  @override
  List<TimeSlotInfo> build(DateTime selectedDate, int pitchId) {
    // Set up periodic timer to mark past slots as unavailable
    _timer = Timer.periodic(const Duration(minutes: 1), (_) {
      final currentSlots = state;
      final updatedSlots = currentSlots.map((slot) {
        if (slot.startTime.isBefore(DateTime.now()) && slot.isAvailable) {
          return slot.copyWith(isAvailable: false, status: SlotStatus.past);
        }
        return slot;
      }).toList();
      
      if (!listEquals(currentSlots, updatedSlots)) {
        state = updatedSlots;
      }
    });
    
    return [];
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
```

### Error Handling with AsyncValue
```dart
class BookingScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AsyncValue<void>>(bookingCreationProvider, (_, state) {
      state.when(
        data: (_) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Booking confirmed!')),
          );
        },
        error: (error, _) {
          String message = 'Booking failed';
          Color snackBarColor = Colors.redAccent;
          
          if (error is SlotUnavailableException) {
            message = 'This slot was just booked by another user';
            snackBarColor = Colors.orange;
            // Auto-refresh availability
            ref.invalidate(availableSlotsProvider);
          } else if (error is BookingLimitReachedException) {
            message = error.message;
            snackBarColor = Colors.amber;
          }
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: snackBarColor,
            ),
          );
        },
        loading: () {
          // Show loading indicator
        },
      );
    });
    
    // Rest of widget build...
  }
}
```

---

## 🏗️ Implementation Architecture

### Data Flow Architecture
```
User Action → Optimistic UI Update → Edge Function → Database Transaction → Realtime Broadcast → All Clients Update
```

### Component Layers

#### 1. Database Layer
- **Constraints:** Unique indexes, check constraints
- **Functions:** Atomic booking creation, validation functions
- **RLS Policies:** Secure data access patterns
- **Indexes:** Optimized for availability queries

#### 2. Backend Layer (Supabase)
- **Edge Functions:** Atomic operations, complex business logic
- **Realtime:** WebSocket subscriptions with RLS
- **Auth:** JWT token management and refresh
- **Storage:** File handling (if needed for receipts, etc.)

#### 3. Client Layer (Flutter + Riverpod)
- **Providers:** State management for booking data
- **Repositories:** Abstract data access layer
- **Models:** Freezed models for immutable data
- **UI:** Reactive widgets with optimistic updates

### Error Handling Architecture
```dart
// Exception hierarchy
abstract class BookingException implements Exception {
  final String code;
  final String message;
  const BookingException(this.code, this.message);
}

class SlotUnavailableException extends BookingException {
  const SlotUnavailableException() 
    : super('SLOT_UNAVAILABLE', 'This slot was just booked by another user');
}

class BookingLimitReachedException extends BookingException {
  const BookingLimitReachedException(String message) 
    : super('LIMIT_REACHED', message);
}

class NetworkException extends BookingException {
  const NetworkException() 
    : super('NETWORK_ERROR', 'Connection issue - please try again');
}
```

---

## ⚡ Performance Considerations

### Database Optimization

#### 1. Indexing Strategy
```sql
-- Primary index for availability queries
CREATE INDEX idx_bookings_availability 
ON bookings (pitch_id, slot_start_time, slot_end_time) 
WHERE status IN ('confirmed', 'pending_payment');

-- Index for user's bookings
CREATE INDEX idx_bookings_user_time 
ON bookings (user_id, slot_start_time) 
WHERE status != 'cancelled';

-- Partial index for active bookings only
CREATE INDEX idx_bookings_active_time_range 
ON bookings (slot_start_time, slot_end_time) 
WHERE status IN ('confirmed', 'pending_payment');
```

#### 2. RLS Policy Optimization
```sql
-- Efficient RLS policy using subqueries
CREATE POLICY "booking_access_policy" ON bookings
  FOR SELECT USING (
    auth.uid() = user_id OR 
    (SELECT is_admin()) OR
    status = 'confirmed' -- Allow viewing confirmed bookings for availability
  );

-- Wrap auth functions in SELECT for better performance
CREATE POLICY "user_bookings_policy" ON bookings
  FOR ALL USING ((SELECT auth.uid()) = user_id);
```

#### 3. Query Optimization
```sql
-- Efficient availability check query
WITH time_slots AS (
  SELECT 
    generate_series(
      start_time::timestamp,
      end_time::timestamp - interval '90 minutes',
      interval '90 minutes'
    ) AS slot_start
  FROM (
    SELECT 
      date_trunc('day', $1::timestamp) + time '05:30' AS start_time,
      date_trunc('day', $1::timestamp) + time '22:30' AS end_time
  ) times
)
SELECT 
  ts.slot_start,
  ts.slot_start + interval '90 minutes' AS slot_end,
  CASE WHEN b.id IS NULL THEN true ELSE false END AS is_available
FROM time_slots ts
LEFT JOIN bookings b ON (
  b.pitch_id = $2 
  AND b.slot_start_time = ts.slot_start
  AND b.status IN ('confirmed', 'pending_payment')
)
WHERE ts.slot_start >= NOW()
ORDER BY ts.slot_start;
```

### Client-Side Optimization

#### 1. Intelligent Caching
```dart
@riverpod
class AvailabilityCache extends _$AvailabilityCache {
  final Map<String, CacheEntry<List<TimeSlotInfo>>> _cache = {};
  
  @override
  Map<String, List<TimeSlotInfo>> build() => {};
  
  List<TimeSlotInfo>? getCached(DateTime date, int pitchId) {
    final key = '${pitchId}_${date.toIso8601String()}';
    final entry = _cache[key];
    
    if (entry != null && !entry.isExpired) {
      return entry.data;
    }
    return null;
  }
  
  void cache(DateTime date, int pitchId, List<TimeSlotInfo> slots) {
    final key = '${pitchId}_${date.toIso8601String()}';
    _cache[key] = CacheEntry(
      data: slots,
      timestamp: DateTime.now(),
      ttl: const Duration(minutes: 5), // Short TTL for availability data
    );
  }
}
```

#### 2. Debounced Updates
```dart
@riverpod
class DebouncedAvailabilityUpdates extends _$DebouncedAvailabilityUpdates {
  Timer? _debounceTimer;
  
  @override
  List<TimeSlotInfo> build() => [];
  
  void updateSlots(List<TimeSlotInfo> newSlots) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      state = newSlots;
    });
  }
  
  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}
```

### Real-Time Subscription Optimization
```dart
@riverpod
class OptimizedRealtimeSubscription extends _$OptimizedRealtimeSubscription {
  RealtimeChannel? _channel;
  
  @override
  AsyncValue<List<TimeSlotInfo>> build(DateTime date, int pitchId) {
    // Only subscribe if date is within reasonable range (e.g., next 30 days)
    if (date.isAfter(DateTime.now().add(const Duration(days: 30)))) {
      return const AsyncValue.data([]);
    }
    
    _setupSubscription(date, pitchId);
    return const AsyncValue.loading();
  }
  
  void _setupSubscription(DateTime date, int pitchId) {
    final supabase = ref.read(supabaseClientProvider);
    
    _channel = supabase.channel('bookings_${pitchId}_${date.millisecondsSinceEpoch}');
    
    _channel!
      .onPostgresChanges(
        event: PostgresChangeEvent.all,
        schema: 'public',
        table: 'bookings',
        callback: (payload) {
          // Process real-time updates
          _handleRealtimeUpdate(payload);
        },
      )
      .subscribe();
  }
  
  @override
  void dispose() {
    _channel?.unsubscribe();
    super.dispose();
  }
}
```

---

## 🚨 Error Handling Strategies

### Client-Side Error Handling

#### 1. Hierarchical Exception Handling
```dart
class BookingErrorHandler {
  static String getErrorMessage(dynamic error) {
    if (error is SlotUnavailableException) {
      return 'This slot was just booked by another user. Please select another time.';
    } else if (error is BookingLimitReachedException) {
      return error.message;
    } else if (error is NetworkException) {
      return 'Connection issue. Please check your internet and try again.';
    } else if (error is TimeoutException) {
      return 'Request timed out. Please try again.';
    } else {
      return 'An unexpected error occurred. Please contact support.';
    }
  }
  
  static Color getErrorColor(dynamic error) {
    if (error is SlotUnavailableException) {
      return Colors.orange; // Warning color for slot conflicts
    } else if (error is BookingLimitReachedException) {
      return Colors.amber; // Different color for limit issues
    } else {
      return Colors.redAccent; // Standard error color
    }
  }
  
  static List<ErrorAction> getRecoveryActions(dynamic error) {
    if (error is SlotUnavailableException) {
      return [
        ErrorAction('Refresh Availability', () => _refreshAvailability()),
        ErrorAction('Pick Different Time', () => _showAlternatives()),
      ];
    } else if (error is NetworkException) {
      return [
        ErrorAction('Retry', () => _retryLastAction()),
        ErrorAction('Go Offline', () => _enableOfflineMode()),
      ];
    }
    return [];
  }
}
```

#### 2. Retry Mechanisms
```dart
@riverpod
class RetryableBookingCreation extends _$RetryableBookingCreation {
  static const maxRetries = 3;
  static const retryDelays = [
    Duration(seconds: 1),
    Duration(seconds: 2),
    Duration(seconds: 4),
  ];
  
  @override
  AsyncValue<void> build() => const AsyncValue.data(null);
  
  Future<void> createBookingWithRetry({
    required BookingRequest request,
    int attempt = 0,
  }) async {
    try {
      await _createBooking(request);
    } catch (e) {
      if (attempt < maxRetries && _isRetryableError(e)) {
        await Future.delayed(retryDelays[attempt]);
        return createBookingWithRetry(
          request: request,
          attempt: attempt + 1,
        );
      }
      rethrow;
    }
  }
  
  bool _isRetryableError(dynamic error) {
    return error is NetworkException || 
           error is TimeoutException ||
           (error is PostgrestException && error.code == 'PGRST301');
  }
}
```

### Server-Side Error Handling

#### 1. Edge Function Error Handling
```typescript
export default async function handler(req: Request) {
  try {
    // Validate input
    const body = await req.json();
    const validationResult = validateBookingRequest(body);
    if (!validationResult.isValid) {
      return new Response(
        JSON.stringify({
          error: 'VALIDATION_ERROR',
          message: validationResult.errors.join(', '),
          details: validationResult.errors,
        }),
        { status: 400 }
      );
    }
    
    // Attempt booking creation
    const result = await createBookingAtomic(body);
    
    return new Response(
      JSON.stringify({ success: true, data: result }),
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Booking creation error:', error);
    
    // Handle specific database errors
    if (error.code === '23505') { // Unique constraint violation
      return new Response(
        JSON.stringify({
          error: 'SLOT_UNAVAILABLE',
          message: 'This slot is no longer available',
          timestamp: new Date().toIso8601String(),
        }),
        { status: 409 }
      );
    }
    
    if (error.code === '23514') { // Check constraint violation
      return new Response(
        JSON.stringify({
          error: 'INVALID_BOOKING',
          message: 'Booking violates system constraints',
          details: error.detail,
        }),
        { status: 422 }
      );
    }
    
    // Generic server error
    return new Response(
      JSON.stringify({
        error: 'SERVER_ERROR',
        message: 'An internal error occurred',
        requestId: crypto.randomUUID(),
      }),
      { status: 500 }
    );
  }
}
```

#### 2. Database-Level Error Handling
```sql
CREATE OR REPLACE FUNCTION create_booking_with_validation(
  p_pitch_id INTEGER,
  p_start_time TIMESTAMPTZ,
  p_end_time TIMESTAMPTZ,
  p_user_id UUID
) RETURNS jsonb AS $$
DECLARE
  v_booking_id UUID;
  v_user_booking_count INTEGER;
  v_result jsonb;
BEGIN
  -- Check user booking limits
  SELECT COUNT(*) INTO v_user_booking_count
  FROM bookings 
  WHERE user_id = p_user_id 
    AND status = 'confirmed'
    AND slot_start_time >= CURRENT_DATE;
    
  IF v_user_booking_count >= 3 THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'BOOKING_LIMIT_REACHED',
      'message', 'You have reached your daily booking limit of 3 slots'
    );
  END IF;
  
  -- Check slot availability
  IF NOT is_slot_available(p_pitch_id, p_start_time, p_end_time) THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'SLOT_UNAVAILABLE',
      'message', 'This time slot is no longer available'
    );
  END IF;
  
  -- Create booking
  INSERT INTO bookings (user_id, pitch_id, slot_start_time, slot_end_time)
  VALUES (p_user_id, p_pitch_id, p_start_time, p_end_time)
  RETURNING id INTO v_booking_id;
  
  RETURN jsonb_build_object(
    'success', true,
    'booking_id', v_booking_id,
    'message', 'Booking created successfully'
  );
  
EXCEPTION
  WHEN unique_violation THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'SLOT_UNAVAILABLE',
      'message', 'This slot was just booked by another user'
    );
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'SERVER_ERROR',
      'message', 'An unexpected error occurred'
    );
END;
$$ LANGUAGE plpgsql;
```

---

## 🧪 Testing Approaches

### Unit Testing Strategy

#### 1. Database Function Testing
```sql
-- Test race condition prevention
BEGIN;
  -- Simulate concurrent booking attempts
  SAVEPOINT before_test;
  
  -- First booking should succeed
  SELECT * FROM create_booking_atomic(1, '2025-06-15 16:00:00+00', '2025-06-15 17:30:00+00', 'user1'::uuid);
  
  -- Second booking for same slot should fail
  SELECT * FROM create_booking_atomic(1, '2025-06-15 16:00:00+00', '2025-06-15 17:30:00+00', 'user2'::uuid);
  
  -- Verify only one booking exists
  SELECT COUNT(*) FROM bookings WHERE slot_start_time = '2025-06-15 16:00:00+00';
  
  ROLLBACK TO before_test;
COMMIT;
```

#### 2. Flutter Widget Testing
```dart
void main() {
  group('BookingConfirmationScreen', () {
    testWidgets('shows loading state during booking creation', (tester) async {
      final container = ProviderContainer(
        overrides: [
          bookingCreationProvider.overrideWith(
            () => MockBookingCreationNotifier()..state = const AsyncValue.loading(),
          ),
        ],
      );
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: BookingConfirmationScreen(
              pitchId: '1',
              pitchName: 'Test Pitch',
              selectedDate: DateTime.now(),
              slotStartTime: DateTime.now(),
              slotEndTime: DateTime.now().add(const Duration(minutes: 90)),
            ),
          ),
        ),
      );
      
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
    
    testWidgets('handles slot unavailable error correctly', (tester) async {
      final container = ProviderContainer(
        overrides: [
          bookingCreationProvider.overrideWith(
            () => MockBookingCreationNotifier()
              ..state = AsyncValue.error(
                const SlotUnavailableException(),
                StackTrace.current,
              ),
          ),
        ],
      );
      
      await tester.pumpWidget(/* ... */);
      await tester.pump(); // Trigger state update
      
      expect(
        find.text('This slot was just booked by another user'),
        findsOneWidget,
      );
    });
  });
}
```

### Integration Testing

#### 1. Concurrent Booking Test
```dart
void main() {
  group('Concurrent Booking Integration Tests', () {
    testWidgets('prevents double booking with concurrent requests', (tester) async {
      final supabase = createTestSupabaseClient();
      
      // Create two concurrent booking requests
      final futures = [
        createBooking(supabase, slotData),
        createBooking(supabase, slotData), // Same slot
      ];
      
      final results = await Future.wait(
        futures,
        eagerError: false,
      );
      
      // One should succeed, one should fail
      final successCount = results.where((r) => r.isSuccess).length;
      final failureCount = results.where((r) => r.isFailure).length;
      
      expect(successCount, equals(1));
      expect(failureCount, equals(1));
      
      // Verify failure reason
      final failure = results.firstWhere((r) => r.isFailure);
      expect(failure.error, isA<SlotUnavailableException>());
    });
  });
}
```

#### 2. Real-Time Update Test
```dart
void main() {
  group('Real-Time Update Tests', () {
    testWidgets('updates availability when another user books slot', (tester) async {
      final container = ProviderContainer();
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(home: AvailabilityScreen()),
        ),
      );
      
      // Verify initial state shows slot as available
      expect(find.text('Available'), findsWidgets);
      
      // Simulate another user booking a slot (via direct DB insert)
      await simulateExternalBooking();
      
      // Wait for real-time update
      await tester.pump(const Duration(seconds: 2));
      
      // Verify slot now shows as unavailable
      expect(find.text('Booked'), findsOneWidget);
    });
  });
}
```

### Load Testing Strategy

#### 1. Database Performance Testing
```sql
-- Test concurrent booking performance
DO $$
DECLARE
    i INTEGER;
    start_time TIMESTAMP;
    end_time TIMESTAMP;
BEGIN
    start_time := clock_timestamp();
    
    -- Simulate 100 concurrent booking attempts
    FOR i IN 1..100 LOOP
        PERFORM create_booking_atomic(
            1, -- pitch_id
            '2025-06-15 16:00:00+00'::timestamptz + (i || ' minutes')::interval,
            '2025-06-15 17:30:00+00'::timestamptz + (i || ' minutes')::interval,
            gen_random_uuid()
        );
    END LOOP;
    
    end_time := clock_timestamp();
    RAISE NOTICE 'Completed 100 bookings in %', end_time - start_time;
END $$;
```

#### 2. Real-Time Connection Testing
```dart
Future<void> testRealtimePerformance() async {
  final clients = <SupabaseClient>[];
  final subscriptions = <RealtimeChannel>[];
  
  // Create 50 concurrent clients
  for (int i = 0; i < 50; i++) {
    final client = createSupabaseClient();
    clients.add(client);
    
    final subscription = client
      .channel('test_channel_$i')
      .onPostgresChanges(
        event: PostgresChangeEvent.all,
        schema: 'public',
        table: 'bookings',
        callback: (payload) {
          // Measure latency
          final latency = DateTime.now().difference(
            DateTime.parse(payload['commit_timestamp']),
          );
          print('Client $i received update in ${latency.inMilliseconds}ms');
        },
      );
      
    subscriptions.add(subscription);
    await subscription.subscribe();
  }
  
  // Perform booking operations and measure real-time propagation
  // ... test logic
  
  // Cleanup
  for (final sub in subscriptions) {
    await sub.unsubscribe();
  }
}
```

---

## 📋 Best Practices & Recommendations

### Database Best Practices

#### 1. Schema Design
- **Use UUIDs for primary keys** when scaling across multiple instances
- **Implement proper indexes** for availability queries
- **Use check constraints** to enforce business rules at database level
- **Design for concurrent access** from the start

#### 2. Performance Optimization
- **Optimize RLS policies** with proper indexing and efficient queries
- **Use connection pooling** to handle concurrent requests
- **Monitor query performance** and optimize slow queries
- **Implement proper caching strategies** for frequently accessed data

#### 3. Security Considerations
- **Enable RLS on all tables** with user data
- **Use least privilege principle** for database roles
- **Validate all inputs** at both client and server level
- **Implement rate limiting** to prevent abuse

### Client-Side Best Practices

#### 1. State Management
- **Use optimistic updates** for better user experience
- **Implement proper error boundaries** to handle failures gracefully
- **Cache data intelligently** with appropriate TTL values
- **Provide clear feedback** for all user actions

#### 2. Real-Time Handling
- **Subscribe only to necessary data** to reduce bandwidth
- **Handle connection drops** gracefully with automatic reconnection
- **Debounce rapid updates** to prevent UI flicker
- **Provide offline capabilities** where possible

#### 3. User Experience
- **Show immediate feedback** for user actions
- **Provide clear error messages** with recovery options
- **Implement pull-to-refresh** for manual data updates
- **Handle edge cases** like time zone differences

### Development Process

#### 1. Testing Strategy
- **Write tests first** (TDD approach)
- **Test race conditions** with concurrent scenarios
- **Mock external dependencies** for unit tests
- **Use integration tests** for critical paths

#### 2. Monitoring & Observability
- **Log all booking attempts** with unique request IDs
- **Monitor real-time connection health**
- **Track performance metrics** for availability queries
- **Set up alerts** for booking failures and system issues

#### 3. Deployment Strategy
- **Use database migrations** for schema changes
- **Implement blue-green deployments** for zero downtime
- **Test in staging** with production-like data
- **Have rollback plans** for critical changes

---

## 🛣️ Implementation Roadmap

### Phase 1: Foundation (Week 1-2)
- [ ] **Database Schema Enhancement**
  - Implement unique constraints for race condition prevention
  - Add optimized indexes for availability queries
  - Create atomic booking creation function
  - Set up RLS policies

- [ ] **Supabase Edge Functions**
  - Create `create_booking_atomic` function
  - Implement proper error handling and validation
  - Add logging and monitoring

- [ ] **Basic Real-Time Setup**
  - Configure Supabase Realtime for bookings table
  - Test real-time subscription functionality
  - Implement connection management

### Phase 2: Client Implementation (Week 3-4)
- [ ] **Riverpod Provider Architecture**
  - Create real-time availability providers
  - Implement optimistic booking creation
  - Add error handling with specific exception types
  - Set up automatic cache invalidation

- [ ] **UI Enhancement**
  - Update AvailabilityScreen for real-time updates
  - Enhance BookingConfirmationScreen with conflict handling
  - Add loading states and error feedback
  - Implement pull-to-refresh functionality

- [ ] **Time-Based Availability**
  - Add timer-based updates for past slots
  - Handle timezone considerations
  - Implement slot status transitions

### Phase 3: Advanced Features (Week 5-6)
- [ ] **Performance Optimization**
  - Implement intelligent caching strategies
  - Add debounced updates for rapid changes
  - Optimize real-time subscription filters
  - Add connection pooling configuration

- [ ] **Error Recovery**
  - Implement retry mechanisms for transient failures
  - Add offline detection and handling
  - Create user-friendly error messages
  - Add alternative slot suggestions

- [ ] **Testing & Monitoring**
  - Write comprehensive unit tests
  - Add integration tests for race conditions
  - Implement performance monitoring
  - Set up error tracking and alerting

### Phase 4: Polish & Production (Week 7-8)
- [ ] **Load Testing**
  - Test concurrent booking scenarios
  - Validate real-time performance at scale
  - Stress test database constraints
  - Optimize based on results

- [ ] **Security Audit**
  - Review RLS policies
  - Test authentication edge cases
  - Validate input sanitization
  - Perform penetration testing

- [ ] **Documentation & Training**
  - Document API endpoints and error codes
  - Create troubleshooting guides
  - Write user guides for booking flow
  - Train support team on system behavior

### Success Metrics
- **Booking Success Rate:** >99.5% for non-conflicting requests
- **Real-Time Update Latency:** <2 seconds for 95% of updates
- **Zero Double Bookings:** Confirmed through load testing
- **User Experience:** <3 second response time for booking operations
- **System Reliability:** >99.9% uptime for booking functionality

---

## 📚 Additional Resources

### Technical Documentation
- [Supabase Realtime Documentation](https://supabase.com/docs/guides/realtime)
- [PostgreSQL Concurrency Control](https://www.postgresql.org/docs/current/mvcc.html)
- [Riverpod Documentation](https://riverpod.dev/)
- [Flutter Testing Guide](https://flutter.dev/docs/testing)

### Performance Guides
- [PostgreSQL Performance Tuning](https://postgresqlco.nf/doc/en/param/)
- [Supabase Performance Tips](https://supabase.com/docs/guides/database/performance)
- [Flutter Performance Best Practices](https://flutter.dev/docs/perf/best-practices)

### Security Resources
- [Row Level Security Best Practices](https://supabase.com/docs/guides/auth/row-level-security)
- [Database Security Checklist](https://wiki.postgresql.org/wiki/Database_Security_Checklist)
- [Flutter Security Guidelines](https://flutter.dev/docs/security)

---

**Research Completed:** June 12, 2025  
**Next Steps:** Begin Phase 1 implementation with database schema enhancements and Edge Function creation.
