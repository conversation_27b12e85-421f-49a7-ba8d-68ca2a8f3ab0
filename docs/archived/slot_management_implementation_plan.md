# Database-Centric Slot Management Implementation Plan

**Project:** Skills Football Booking App  
**Date:** 2025-06-18  
**Status:** Planning Phase  
**Related ADR:** ADR-007

## Executive Summary

This document outlines the detailed implementation plan for migrating from client-side slot status calculation to a database-centric slot management system. The migration will be conducted in 6 phases over approximately 6-8 weeks to ensure zero downtime and data integrity.

## Current System Analysis

### Current Architecture Issues
1. **Client-Side Calculation Complexity**
   ```dart
   // Complex and error-prone logic in real_time_availability_provider.dart
   final isBooked = bookings.any((booking) {
     final startMatch = booking.slotStartTime.isAtSameMomentAs(currentSlotStart);
     final endMatch = booking.slotEndTime.isAtSameMomentAs(currentSlotEnd);
     return startMatch && endMatch; // Timezone/precision issues
   });
   ```

2. **Performance Bottlenecks**
   - Slot status recalculated on every screen render
   - Complex DateTime comparisons in client
   - Multiple database queries to determine availability

3. **Code Duplication & Maintenance Issues**
   - Slot generation logic scattered across multiple files
   - Debug helpers for troubleshooting calculation issues
   - Inconsistent slot status determination

### Files Currently Involved in Slot Management

#### Core Files (Major Changes Required)
```
lib/features/availability/application/
├── real_time_availability_provider.dart   # ~300 lines, complex calculations
├── availability_service.dart               # Provider definitions

lib/features/availability/domain/
├── time_slot_info_model.dart              # Model needs database fields
└── time_slot_calculator.dart              # Remove entirely

lib/features/availability/presentation/
├── widgets/availability_details.dart       # Simplified data fetching
├── widgets/slot_list_item.dart             # Direct status display
└── availability_screen.dart                # No changes needed
```

#### Debug/Helper Files (To Remove/Refactor)
```
lib/debug/
├── availability_debug_helper.dart         # Remove after migration
├── slot_booking_debugger.dart            # Created for current issues
└── test_real_time_updates.dart           # Update for new system

docs/
├── DEBUGGING_MISSING_SLOT.md             # Archive after fix
└── known_issues.md                       # Update with resolution
```

#### Database Files (New)
```
supabase/migrations/
├── 20250618_create_time_slots_table.sql
├── 20250618_create_slot_functions.sql
├── 20250618_create_slot_triggers.sql
└── 20250618_migrate_existing_data.sql
```

## Phase-by-Phase Implementation Plan

### Phase 1: Database Schema Design & Setup (Week 1)

#### Day 1-2: Schema Design
```sql
-- Primary table for slot storage
CREATE TABLE time_slots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  pitch_id UUID REFERENCES pitches(id) ON DELETE CASCADE,
  slot_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_booked BOOLEAN DEFAULT FALSE,
  is_available BOOLEAN DEFAULT TRUE,
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  price_per_hour DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_time_range CHECK (end_time > start_time),
  CONSTRAINT future_or_today CHECK (slot_date >= CURRENT_DATE)
);

-- Indexes for performance
CREATE INDEX idx_time_slots_pitch_date ON time_slots(pitch_id, slot_date);
CREATE INDEX idx_time_slots_available ON time_slots(is_available, is_booked);
CREATE INDEX idx_time_slots_booking ON time_slots(booking_id) WHERE booking_id IS NOT NULL;
CREATE UNIQUE INDEX idx_time_slots_unique ON time_slots(pitch_id, slot_date, start_time);
```

#### Day 3-4: Database Functions
```sql
-- Function to generate time slots for a specific pitch and date
CREATE OR REPLACE FUNCTION generate_time_slots(
  p_pitch_id UUID,
  p_date DATE,
  p_force_regenerate BOOLEAN DEFAULT FALSE
) RETURNS TABLE(slots_created INTEGER, message TEXT) AS $$
DECLARE
  pitch_settings RECORD;
  current_time TIME;
  slot_duration INTERVAL;
  slots_count INTEGER := 0;
BEGIN
  -- Validate inputs
  IF p_date < CURRENT_DATE THEN
    RETURN QUERY SELECT 0, 'Cannot generate slots for past dates';
    RETURN;
  END IF;
  
  -- Get pitch settings
  SELECT opening_time, closing_time, slot_duration_minutes, price_per_hour
  INTO pitch_settings
  FROM pitches 
  WHERE id = p_pitch_id;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT 0, 'Pitch not found';
    RETURN;
  END IF;
  
  -- Check if slots already exist
  IF EXISTS(SELECT 1 FROM time_slots WHERE pitch_id = p_pitch_id AND slot_date = p_date) THEN
    IF NOT p_force_regenerate THEN
      RETURN QUERY SELECT 0, 'Slots already exist for this date';
      RETURN;
    ELSE
      DELETE FROM time_slots WHERE pitch_id = p_pitch_id AND slot_date = p_date;
    END IF;
  END IF;
  
  -- Generate slots
  slot_duration := make_interval(mins => pitch_settings.slot_duration_minutes);
  current_time := pitch_settings.opening_time;
  
  WHILE current_time + slot_duration <= pitch_settings.closing_time LOOP
    INSERT INTO time_slots (
      pitch_id, slot_date, start_time, end_time, price_per_hour
    ) VALUES (
      p_pitch_id, p_date, current_time, current_time + slot_duration, pitch_settings.price_per_hour
    );
    
    slots_count := slots_count + 1;
    current_time := current_time + slot_duration;
  END LOOP;
  
  RETURN QUERY SELECT slots_count, format('%s slots created successfully', slots_count);
END;
$$ LANGUAGE plpgsql;
```

### Phase 2: Database Implementation & Migration Scripts (Week 2)

#### Migration Strategy
1. **Parallel System Operation:** Run both systems during transition
2. **Slot Data Generation:** Bulk generate slots for existing pitches
3. **Booking Status Sync:** Ensure existing bookings update slot status
4. **Validation Phase:** Compare old vs new system results
5. **Gradual Cutover:** Feature-flag controlled migration
6. **Cleanup Phase:** Remove old calculation logic

### Phase 3: API Layer Updates (Week 3)

#### New Simplified Provider
```dart
class DatabaseSlotProvider extends StateNotifier<AsyncValue<List<TimeSlotInfo>>> {
  final SupabaseClient _supabase = Supabase.instance.client;

  DatabaseSlotProvider() : super(const AsyncValue.loading());

  Future<void> loadSlots(String pitchId, DateTime date) async {
    try {
      state = const AsyncValue.loading();
      
      final response = await _supabase
          .from('time_slots')
          .select('*')
          .eq('pitch_id', pitchId)
          .eq('slot_date', date.toIso8601String().split('T')[0])
          .eq('is_available', true)
          .order('start_time');

      final slots = response
          .map((json) => TimeSlotInfo.fromDatabaseJson(json))
          .toList();

      state = AsyncValue.data(slots);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Stream<List<TimeSlotInfo>> watchSlots(String pitchId, DateTime date) {
    return _supabase
        .from('time_slots')
        .stream(primaryKey: ['id'])
        .eq('pitch_id', pitchId)
        .eq('slot_date', date.toIso8601String().split('T')[0])
        .eq('is_available', true)
        .order('start_time')
        .map((data) => data
            .map((json) => TimeSlotInfo.fromDatabaseJson(json))
            .toList());
  }
}
```

### Phase 4: Client Refactoring (Week 4)

#### Updated TimeSlotInfo Model
```dart
class TimeSlotInfo {
  final String id;
  final DateTime startTime;
  final DateTime endTime;
  final bool isBooked;
  final bool isAvailable;
  final String? bookingId;
  final double price;

  const TimeSlotInfo({
    required this.id,
    required this.startTime,
    required this.endTime,
    required this.isBooked,
    required this.isAvailable,
    this.bookingId,
    required this.price,
  });

  factory TimeSlotInfo.fromDatabaseJson(Map<String, dynamic> json) {
    final date = DateTime.parse(json['slot_date']);
    final startTimeParts = (json['start_time'] as String).split(':');
    final endTimeParts = (json['end_time'] as String).split(':');
    
    final startTime = DateTime(
      date.year, date.month, date.day,
      int.parse(startTimeParts[0]),
      int.parse(startTimeParts[1]),
    );
    
    final endTime = DateTime(
      date.year, date.month, date.day,
      int.parse(endTimeParts[0]),
      int.parse(endTimeParts[1]),
    );

    return TimeSlotInfo(
      id: json['id'],
      startTime: startTime,
      endTime: endTime,
      isBooked: json['is_booked'] ?? false,
      isAvailable: json['is_available'] ?? true,
      bookingId: json['booking_id'],
      price: (json['price_per_hour'] ?? 0.0).toDouble(),
    );
  }
}
```

### Phase 5: Testing & Migration (Week 5)

#### Feature Flag Implementation
```dart
class FeatureFlagService {
  static const String _databaseSlotsKey = 'use_database_slots';
  
  static bool get useDatabaseSlots {
    // Start with small percentage
    return _getUserBucket() < 0.1; // 10% of users
  }
  
  static double _getUserBucket() {
    // Consistent user bucketing logic
    final userId = Supabase.instance.client.auth.currentUser?.id ?? 'anonymous';
    return (userId.hashCode % 100) / 100.0;
  }
}
```

### Phase 6: Cleanup & Documentation (Week 6)

#### Files to Remove After Migration
```
Files to Remove:
├── lib/debug/availability_debug_helper.dart
├── lib/debug/slot_booking_debugger.dart
└── docs/DEBUGGING_MISSING_SLOT.md

Files to Refactor:
├── lib/features/availability/application/real_time_availability_provider.dart
│   └── Remove complex calculation logic
├── lib/features/availability/domain/time_slot_info_model.dart
│   └── Update to match database schema
└── test/features/availability/
    └── Update all tests for new system
```

## Code Cleanup Strategy

### Current Code Analysis

Let me examine the current files to understand what needs to be cleaned up:

#### 1. real_time_availability_provider.dart
- **Current:** Complex slot generation and booking status calculation
- **Target:** Simple database query wrapper
- **Cleanup:** Remove `_calculateAvailableSlots` method and related complexity

#### 2. availability_debug_helper.dart
- **Current:** Debug utilities for troubleshooting slot calculations
- **Target:** Remove entirely (issues will be resolved by database approach)
- **Cleanup:** Archive for reference, then delete

#### 3. slot_booking_debugger.dart
- **Current:** Recently created for debugging current slot offset issue
- **Target:** Remove after migration validates fix
- **Cleanup:** Delete once new system is confirmed working

#### 4. DEBUGGING_MISSING_SLOT.md
- **Current:** Documentation of current slot marking issues
- **Target:** Archive as historical reference
- **Cleanup:** Move to archived_docs folder

### Detailed Cleanup Plan

#### Week 4-5: During Migration
1. **Keep parallel systems** running for comparison
2. **Feature flag** to switch between implementations
3. **Log comparison results** to validate accuracy

#### Week 6: Post-Migration Cleanup
1. **Remove deprecated files** completely
2. **Simplify remaining files** to remove unused code
3. **Update documentation** to reflect new architecture
4. **Archive debugging materials** for historical reference

## Risk Mitigation

### Technical Risks
1. **Data Migration Issues**
   - **Mitigation:** Comprehensive backups, parallel operation, gradual rollout

2. **Performance Degradation**
   - **Mitigation:** Proper indexing, query optimization, performance testing

3. **Real-time Updates Broken**
   - **Mitigation:** Maintain existing stream structure, test thoroughly

### Business Risks
1. **User Experience Disruption**
   - **Mitigation:** Feature flags, gradual rollout, rollback procedures

2. **Revenue Loss**
   - **Mitigation:** Extensive testing, monitoring, quick rollback capability

## Success Metrics

### Functional Metrics
- **Slot Accuracy:** 100% correct slot status display (fixes current 3-slot offset issue)
- **Performance:** <100ms slot loading time
- **Consistency:** 0% discrepancies between clients
- **Reliability:** 99.9%+ availability during migration

### Technical Metrics
- **Code Complexity:** 50% reduction in availability-related code
- **Database Load:** <10% increase in database operations  
- **Client Processing:** 80% reduction in client-side calculations
- **Bug Reports:** 90% reduction in slot-related issues

## Timeline

| Phase | Duration | Key Deliverables |
|-------|----------|-----------------|
| Phase 1: Planning & Design | 1 week | Database schema, migration plan |
| Phase 2: Database Implementation | 1 week | Tables, functions, triggers |
| Phase 3: API Updates | 1 week | Updated endpoints, providers |
| Phase 4: Client Refactoring | 1 week | Updated Flutter code |
| Phase 5: Testing & Migration | 1 week | Testing, gradual rollout |
| Phase 6: Cleanup | 1 week | Code cleanup, documentation |

**Total Estimated Time:** 6 weeks  
**Risk Buffer:** +2 weeks  
**Target Completion:** End of July 2025

---

**Document Status:** Draft v1.0  
**Next Review:** Weekly during implementation  
**Owner:** Development Team
