# Debugging Missing 15:00-16:00 Slot Issue

## 🔍 **Problem Analysis**

**Issue**: Slot 15:00-16:00 is not showing up on availability screen despite being available
**Database State**: Has bookings for 12:00-13:00, 13:00-14:00, and 14:00-15:00 (all confirmed)
**Expected**: Slot 15:00-16:00 should be visible as available

## 📊 **Current Database Data**
```json
[
  { "slot_start_time": "2025-06-13 12:00:00+00", "slot_end_time": "2025-06-13 13:00:00+00", "status": "confirmed", "pitch_id": 1 },
  { "slot_start_time": "2025-06-13 13:00:00+00", "slot_end_time": "2025-06-13 14:00:00+00", "status": "confirmed", "pitch_id": 1 },
  { "slot_start_time": "2025-06-13 14:00:00+00", "slot_end_time": "2025-06-13 15:00:00+00", "status": "confirmed", "pitch_id": 1 }
]
```

## 🔧 **Debugging Tools Added**

### 1. **Enhanced Real-Time Provider Logging**
File: `lib/features/availability/application/real_time_availability_provider.dart`

**New logs will show:**
- 🔄 Real-time updates received with booking count
- 📅 Each booking details (start/end times, status, ID)
- 🔍 Slot calculation details
- ⏰ Each generated slot with AVAILABLE/BOOKED status
- 📊 Summary of total/available/booked slots

### 2. **Enhanced Static Provider Logging**
File: `lib/features/availability/application/availability_service.dart`

**New logs will show:**
- 📍 Static provider calls with date/pitch
- 📍 Booking data retrieved from database

### 3. **Debug Helper Utility**
File: `lib/debug/availability_debug_helper.dart`

**Can be used to manually log:**
- Expected slot generation based on pitch settings
- Comparison with actual booking data

## 🧪 **How to Debug**

### **Step 1: Run the App with Logging**
1. Open the app and navigate to availability screen for June 13, 2025
2. Check Flutter console/logs for the new debug output
3. Look for these key log patterns:

```
🔄 Real-time update received: X bookings for 2025-06-13
📅 Booking 0: 12:00 - 13:00 (Status: confirmed, ID: 85)
📅 Booking 1: 13:00 - 14:00 (Status: confirmed, ID: 86)
📅 Booking 2: 14:00 - 15:00 (Status: confirmed, ID: 87)
🔍 Starting slot calculation for date: 2025-06-13
🕐 Operating hours: 09:00 - 22:00
⏰ Slot 0: 09:00 - 10:00 | AVAILABLE
⏰ Slot 1: 10:00 - 11:00 | AVAILABLE
...
⏰ Slot 6: 15:00 - 16:00 | AVAILABLE  <- This should appear!
```

### **Step 2: Check Pitch Settings**
Verify pitch settings in database:
- What are the `open_time` and `close_time` for pitch 1?
- What is the `slot_duration_minutes`?
- Is there a constraint preventing 15:00-16:00 generation?

### **Step 3: Check for UI Filtering**
Look for any UI-level filtering that might hide available slots:
- Time-based filtering (past slots, etc.)
- Business logic filtering
- UI rendering issues

## 🔍 **Most Likely Causes**

### **1. Pitch Settings Issue**
- **Close time too early**: If pitch closes at 15:00, then 15:00-16:00 wouldn't generate
- **Slot duration mismatch**: If duration is not 60 minutes, slot boundaries might not align

### **2. Time Zone Issues**
- **UTC vs Local time**: Database times are in UTC (+00), but UI might be using local time
- **Date boundary issues**: Slot generation might be using wrong date boundaries

### **3. Real-time vs Static Provider Mismatch**
- **Provider inconsistency**: Different providers might be generating different slot lists
- **Caching issues**: Old data being cached

### **4. UI Filtering**
- **Past slot filtering**: If current time is after 15:00, slot might be filtered as "past"
- **Booking limit filtering**: User might have reached booking limits

## 🎯 **Expected Resolution**

With the enhanced logging, you should be able to see:
1. **What bookings are being received** from the database
2. **What slots are being generated** by the calculation function
3. **Whether 15:00-16:00 is generated** but filtered out later
4. **The exact pitch settings** being used for calculation

This will help pinpoint exactly where the 15:00-16:00 slot is being lost in the process.

## 📋 **Next Steps**

1. **Run app and collect logs** from availability screen
2. **Share the log output** showing slot generation
3. **Check pitch settings** in Supabase dashboard
4. **Verify UI filtering logic** if slot generation looks correct

The enhanced logging should make it very clear where the issue is occurring!
