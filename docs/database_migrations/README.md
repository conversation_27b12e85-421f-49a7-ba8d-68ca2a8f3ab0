# Database Migrations

This directory contains all database migration files for the Skills booking application.

## Migration History

### Migration 002: Pricing & Limits ✅
- Added `price_per_hour` field to pitch_settings
- Added `max_bookings_per_user` field to pitch_settings
- **Status**: Applied

### Migration 003: Race Condition Prevention ✅
- Added unique constraints for booking slots
- Implemented atomic booking functions
- **Status**: Applied

### Migration 004: ADR-006 Complete Implementation ✅
- Comprehensive ADR-006 implementation combining migrations 002 & 003
- Real-time availability system with race condition prevention
- **Status**: Applied and operational

## Current Database Status

```sql
-- Current pitch_settings schema includes:
price_per_hour          | numeric   | NO  | 0.00
max_bookings_per_user   | integer   | NO  | 4

-- Booking constraints include:
unique_active_booking_slots     | UNIQUE (pitch_id, slot_start_time, slot_end_time)
no_past_bookings               | CHECK ((slot_start_time > (now() - '00:05:00'::interval)))
valid_time_range               | CHECK ((slot_start_time < slot_end_time))

-- Functions include:
create_booking_atomic          | Atomic booking creation with race condition prevention
validate_booking_slot          | Slot availability validation
```

## Integration Status

- ✅ **Database**: All migrations applied successfully
- ✅ **Flutter App**: Real-time availability system operational
- ✅ **Edge Functions**: Atomic booking function deployed
- ✅ **ADR-006**: Fully implemented and operational
