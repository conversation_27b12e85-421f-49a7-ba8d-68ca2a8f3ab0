-- Migration: Add race condition prevention constraints and indexes
-- Date: 2025-06-12
-- Description: Implements unique constraints and indexes to prevent double bookings
-- Related: ADR-006 Real-Time Availability and Race Condition Prevention

BEGIN;

-- 1. Add unique constraint to prevent overlapping bookings at database level
-- This constraint ensures that only one active booking can exist for any given time slot and pitch
CREATE UNIQUE INDEX IF NOT EXISTS unique_active_booking_slots 
ON bookings (pitch_id, slot_start_time, slot_end_time) 
WHERE status IN ('confirmed', 'pending_payment');

-- 2. Add additional validation constraints
DO $$
BEGIN
    -- Add valid time range constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'valid_time_range'
    ) THEN
        ALTER TABLE bookings 
        ADD CONSTRAINT valid_time_range 
        CHECK (slot_end_time > slot_start_time);
    END IF;
    
    -- Add no past bookings constraint if it doesn't exist
    -- This constraint only applies to NEW bookings, not existing ones
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'no_past_bookings'
    ) THEN
        -- Use NOT VALID to add constraint without checking existing data
        ALTER TABLE bookings 
        ADD CONSTRAINT no_past_bookings 
        CHECK (slot_start_time > NOW() - INTERVAL '5 minutes') NOT VALID;
        
        -- Validate the constraint (this will only check future inserts/updates)
        ALTER TABLE bookings VALIDATE CONSTRAINT no_past_bookings;
    END IF;
END $$;

-- 3. Add optimized indexes for real-time queries
CREATE INDEX IF NOT EXISTS idx_bookings_realtime_availability 
ON bookings (pitch_id, slot_start_time, slot_end_time, status) 
WHERE status NOT IN ('cancelled_by_user', 'cancelled_by_admin');

-- Index specifically for user's bookings (used in my_bookings screen)
CREATE INDEX IF NOT EXISTS idx_bookings_user_active 
ON bookings (user_id, slot_start_time, status) 
WHERE status IN ('confirmed', 'pending_payment');

-- Index for efficient date range queries in availability calculation
CREATE INDEX IF NOT EXISTS idx_bookings_date_range 
ON bookings (pitch_id, slot_start_time) 
WHERE status IN ('confirmed', 'pending_payment');

-- 4. Create atomic booking validation function
CREATE OR REPLACE FUNCTION validate_booking_slot(
  p_pitch_id INTEGER,
  p_start_time TIMESTAMPTZ, 
  p_end_time TIMESTAMPTZ
) RETURNS BOOLEAN AS $$
DECLARE
  existing_booking_count INTEGER;
BEGIN
  -- Check for any overlapping active bookings
  SELECT COUNT(*) INTO existing_booking_count
  FROM bookings 
  WHERE pitch_id = p_pitch_id 
    AND status IN ('confirmed', 'pending_payment')
    AND (
      -- Check for overlapping time slots:
      -- New booking overlaps if any of these conditions are true:
      (slot_start_time <= p_start_time AND slot_end_time > p_start_time) OR
      (slot_start_time < p_end_time AND slot_end_time >= p_end_time) OR  
      (slot_start_time >= p_start_time AND slot_end_time <= p_end_time)
    );
    
  RETURN existing_booking_count = 0;
END;
$$ LANGUAGE plpgsql;

-- 5. Create atomic booking creation function to prevent race conditions
-- First, drop any existing versions of the function
DROP FUNCTION IF EXISTS create_booking_atomic(UUID, INTEGER, TIMESTAMPTZ, TIMESTAMPTZ);
DROP FUNCTION IF EXISTS create_booking_atomic(p_user_id UUID, p_pitch_id INTEGER, p_slot_start_time TIMESTAMPTZ, p_slot_end_time TIMESTAMPTZ);

-- Create the new version
CREATE FUNCTION create_booking_atomic(
  p_user_id UUID,
  p_pitch_id INTEGER,
  p_slot_start_time TIMESTAMPTZ,
  p_slot_end_time TIMESTAMPTZ
) RETURNS INTEGER AS $$
DECLARE
  v_booking_id INTEGER;
  v_pitch_settings RECORD;
  v_user_booking_count INTEGER;
BEGIN
  -- Start transaction (function automatically runs in transaction)
  
  -- 1. Validate that the slot is still available
  IF NOT validate_booking_slot(p_pitch_id, p_slot_start_time, p_slot_end_time) THEN
    RAISE EXCEPTION 'SLOT_UNAVAILABLE: This time slot is no longer available'
      USING ERRCODE = '23505'; -- Use unique constraint violation code
  END IF;
  
  -- 2. Get pitch settings for booking limit validation
  SELECT * INTO v_pitch_settings 
  FROM pitch_settings 
  WHERE id = p_pitch_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'INVALID_PITCH: Pitch not found'
      USING ERRCODE = '23503'; -- Foreign key violation code
  END IF;
  
  -- 3. Check user's booking limit
  SELECT COUNT(*) INTO v_user_booking_count
  FROM bookings
  WHERE user_id = p_user_id
    AND status IN ('confirmed', 'pending_payment')
    AND slot_end_time > NOW(); -- Only count future bookings
    
  IF v_user_booking_count >= v_pitch_settings.max_bookings_per_user THEN
    RAISE EXCEPTION 'BOOKING_LIMIT_REACHED: Maximum bookings limit reached'
      USING ERRCODE = 'P0001'; -- Custom error code
  END IF;
  
  -- 4. Create the booking atomically
  INSERT INTO bookings (
    user_id, 
    pitch_id, 
    slot_start_time, 
    slot_end_time, 
    status
  ) VALUES (
    p_user_id,
    p_pitch_id,
    p_slot_start_time,
    p_slot_end_time,
    'confirmed'
  ) RETURNING id INTO v_booking_id;
  
  RETURN v_booking_id;
END;
$$ LANGUAGE plpgsql;

-- 6. Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Only create trigger if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'update_bookings_updated_at'
  ) THEN
    CREATE TRIGGER update_bookings_updated_at
      BEFORE UPDATE ON bookings
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- 7. Add comments for documentation
COMMENT ON INDEX unique_active_booking_slots IS 'Prevents double bookings by ensuring unique time slots per pitch';
COMMENT ON FUNCTION validate_booking_slot IS 'Validates that a booking slot is available before creation';
COMMENT ON FUNCTION create_booking_atomic IS 'Atomically creates a booking with race condition prevention';

COMMIT;

-- Display success message
DO $$
BEGIN
  RAISE NOTICE 'Migration 003 completed successfully: Race condition prevention constraints and functions added';
END
$$;
