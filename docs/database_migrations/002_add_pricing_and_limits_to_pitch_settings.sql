-- Migration: Add pricing and booking limits to pitch_settings table
-- Date: 2025-06-04
-- Description: Adds price_per_hour and max_bookings_per_user fields to support dynamic pricing and limits

BEGIN;

-- Add new columns to pitch_settings table
ALTER TABLE pitch_settings 
ADD COLUMN IF NOT EXISTS price_per_hour DECIMAL(10,2) NOT NULL DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS max_bookings_per_user INTEGER NOT NULL DEFAULT 4;

-- Add comments for documentation
COMMENT ON COLUMN pitch_settings.price_per_hour IS 'Hourly rate in MWK for booking calculations';
COMMENT ON COLUMN pitch_settings.max_bookings_per_user IS 'Maximum number of active bookings allowed per user for this pitch';

-- Update existing records with default values (adjust as needed for your business)
-- Example: Set initial price to 5000 MWK per hour (adjust to your actual pricing)
UPDATE pitch_settings 
SET price_per_hour = 5000.00, 
    max_bookings_per_user = 4 
WHERE price_per_hour = 0.00; -- Only update records that haven't been manually set

-- Add check constraints for data validation
ALTER TABLE pitch_settings 
ADD CONSTRAINT check_price_per_hour_positive CHECK (price_per_hour >= 0),
ADD CONSTRAINT check_max_bookings_positive CHECK (max_bookings_per_user > 0);

-- Create index for better query performance on price lookups
CREATE INDEX IF NOT EXISTS idx_pitch_settings_price_per_hour 
ON pitch_settings(price_per_hour);

-- Update the updated_at timestamp for all modified records
UPDATE pitch_settings 
SET updated_at = NOW() 
WHERE true;

COMMIT;
