# Supabase Backend Reconstruction Plan
**Date:** June 20, 2025  
**Status:** Critical Infrastructure Recovery  
**ADR-007 Compatibility:** Maintains 85% complete implementation

---

## 🚨 **Executive Summary**

Our Supabase backend project was completely deleted, requiring full reconstruction. This plan ensures:
- **Zero data loss** (no user data existed in deleted project)
- **ADR-007 compatibility** maintained (85% complete database-centric slot management)
- **3-slot offset bug fix** preserved through comprehensive schema recreation
- **Minimal downtime** during reconstruction process

---

## 📋 **Phase 1: New Supabase Project Setup**

### **Step 1: Create New Supabase Project** (5 minutes)
1. **Go to Supabase Dashboard**: https://supabase.com/dashboard
2. **Create New Project**:
   - Project Name: `skillz-football-booking` (or similar)
   - Database Password: Generate strong password and save securely
   - Region: Choose closest to your users
   - Pricing Plan: Free tier (can upgrade later)

3. **Save Critical Information**:
   ```
   Project URL: https://[PROJECT_ID].supabase.co
   Anon Key: eyJ... (public key)
   Service Role Key: eyJ... (private key - keep secure)
   Database Password: [SECURE_PASSWORD]
   ```

### **Step 2: Configure Authentication** (5 minutes)
1. **Navigate to Authentication > Settings**
2. **Enable Email/Password Authentication**
3. **Configure Email Templates** (optional for now)
4. **Set Site URL**: 
   - For development: `http://localhost:3000`
   - For production: Your app's domain
5. **Configure Redirect URLs**:
   - Add: `io.supabase.skillz://login-callback/`
   - Add: `http://localhost:3000/auth/callback`

### **Step 3: Database Configuration** (2 minutes)
1. **Navigate to Database > Settings**
2. **Enable Row Level Security** (should be enabled by default)
3. **Configure Connection Pooling** (default settings OK for now)

---

## 📊 **Phase 2: Schema Recreation**

### **Step 4: Execute Core Schema** (10 minutes)
1. **Navigate to SQL Editor in Supabase Dashboard**
2. **Execute Complete Schema Script**:
   ```sql
   -- Copy and paste entire content from:
   -- docs/backend_reconstruction/complete_database_schema.sql
   ```
3. **Verify Execution**:
   - Check for any error messages
   - Confirm all tables created successfully
   - Verify triggers and functions exist

### **Step 5: Validate Schema Creation** (5 minutes)
**Run Validation Queries**:
```sql
-- Check tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check ADR-007 critical components
SELECT routine_name FROM information_schema.routines 
WHERE routine_name IN ('generate_time_slots', 'update_slot_booking_status');

-- Check triggers
SELECT trigger_name, event_manipulation, event_object_table 
FROM information_schema.triggers 
WHERE trigger_schema = 'public';
```

### **Step 6: Execute Seed Data** (5 minutes)
1. **Execute Seed Data Script**:
   ```sql
   -- Copy and paste entire content from:
   -- docs/backend_reconstruction/seed_data.sql
   ```
2. **Verify Data Creation**:
   ```sql
   SELECT COUNT(*) FROM pitch_settings;
   SELECT COUNT(*) FROM time_slots;
   ```

---

## 🔧 **Phase 3: Flutter App Configuration**

### **Step 7: Update Environment Configuration** (5 minutes)
1. **Update `.env` file**:
   ```env
   SUPABASE_URL=https://[YOUR_NEW_PROJECT_ID].supabase.co
   SUPABASE_ANON_KEY=[YOUR_NEW_ANON_KEY]
   ```

2. **Verify Environment Loading**:
   ```bash
   flutter clean
   flutter pub get
   flutter run
   ```

### **Step 8: Test Basic Connectivity** (10 minutes)
1. **Launch Flutter App**
2. **Test Authentication**:
   - Try sign up with test email
   - Verify email confirmation (check Supabase Auth logs)
   - Test login functionality

3. **Test Database Connectivity**:
   - Navigate to availability screen
   - Verify pitch settings load
   - Check for any connection errors in logs

---

## 🧪 **Phase 4: ADR-007 Validation**

### **Step 9: Validate Slot Generation** (10 minutes)
1. **Test Slot Generation Function**:
   ```sql
   -- Test slot generation for tomorrow
   SELECT * FROM generate_time_slots(1, CURRENT_DATE + INTERVAL '1 day', false);
   ```

2. **Verify Slot Data**:
   ```sql
   -- Check generated slots
   SELECT pitch_id, slot_date, COUNT(*) as slot_count
   FROM time_slots 
   GROUP BY pitch_id, slot_date 
   ORDER BY pitch_id, slot_date;
   ```

### **Step 10: Test Real-time Streaming** (10 minutes)
1. **In Flutter App**:
   - Navigate to availability screen
   - Select a pitch and date
   - Verify slots load correctly

2. **Test Real-time Updates**:
   - In Supabase SQL Editor, manually update a slot:
     ```sql
     UPDATE time_slots 
     SET is_booked = true 
     WHERE id = (SELECT id FROM time_slots LIMIT 1);
     ```
   - Verify Flutter app updates in real-time

### **Step 11: Validate 3-Slot Offset Fix** (15 minutes)
1. **Create Test User** (if not exists):
   - Sign up through Flutter app
   - Note the user ID from Supabase Auth

2. **Create Test Booking**:
   ```sql
   INSERT INTO bookings (
     user_id,
     pitch_id,
     slot_start_time,
     slot_end_time,
     status
   ) VALUES (
     '[USER_ID_FROM_AUTH]',
     1,
     CURRENT_DATE + INTERVAL '1 day' + TIME '10:00:00',
     CURRENT_DATE + INTERVAL '1 day' + TIME '11:00:00',
     'confirmed'
   );
   ```

3. **Verify Trigger Functionality**:
   ```sql
   -- Check that slot was marked as booked
   SELECT * FROM time_slots 
   WHERE pitch_id = 1 
     AND slot_date = CURRENT_DATE + INTERVAL '1 day'
     AND start_time = '10:00:00'
     AND is_booked = true;
   ```

4. **Test in Flutter App**:
   - Navigate to availability for tomorrow
   - Verify the 10:00-11:00 slot shows as booked
   - Confirm no offset issues (slot times match exactly)

---

## ⚠️ **Phase 5: Risk Mitigation & Rollback**

### **Rollback Procedures**
1. **If Schema Creation Fails**:
   - Delete and recreate Supabase project
   - Review SQL script for syntax errors
   - Execute schema in smaller chunks

2. **If Flutter App Won't Connect**:
   - Verify environment variables are correct
   - Check Supabase project URL and keys
   - Confirm CORS settings in Supabase

3. **If ADR-007 Functions Don't Work**:
   - Check function creation logs in SQL Editor
   - Verify trigger exists and is active
   - Test functions individually

### **Backup Strategy**
1. **Export Schema** (after successful creation):
   ```bash
   # Use Supabase CLI to backup schema
   supabase db dump --schema-only > backup_schema.sql
   ```

2. **Document Configuration**:
   - Save all environment variables securely
   - Document any custom configurations
   - Keep copy of working SQL scripts

---

## 📊 **Success Criteria Checklist**

### **✅ Infrastructure Setup**
- [ ] New Supabase project created
- [ ] Authentication configured
- [ ] Environment variables updated
- [ ] Flutter app connects successfully

### **✅ Database Schema**
- [ ] All tables created (profiles, pitch_settings, bookings, time_slots)
- [ ] All indexes created
- [ ] All RLS policies active
- [ ] All triggers functioning

### **✅ ADR-007 Functionality**
- [ ] `generate_time_slots` function works
- [ ] `update_slot_booking_status` trigger works
- [ ] Real-time streaming functional
- [ ] 3-slot offset bug remains fixed

### **✅ Integration Testing**
- [ ] Flutter app loads pitch settings
- [ ] Slot generation works from app
- [ ] Real-time updates work
- [ ] Booking creation updates slots correctly
- [ ] No offset issues in slot display

---

## 🕐 **Timeline Estimate**

| Phase | Task | Duration | Dependencies |
|-------|------|----------|--------------|
| 1 | Supabase Project Setup | 15 min | None |
| 2 | Schema Recreation | 20 min | Phase 1 complete |
| 3 | Flutter Configuration | 15 min | Phase 2 complete |
| 4 | ADR-007 Validation | 35 min | Phase 3 complete |
| 5 | Risk Mitigation Setup | 15 min | All phases |

**Total Estimated Time**: 1.5 - 2 hours  
**Critical Path**: Schema → Flutter → ADR-007 Validation  
**Parallel Work**: Documentation can be updated during testing

---

## 🔗 **Dependencies & Prerequisites**

### **Required Access**
- Supabase account with project creation permissions
- Flutter development environment set up
- Access to modify `.env` file
- SQL execution permissions in Supabase

### **Required Files**
- `docs/backend_reconstruction/complete_database_schema.sql`
- `docs/backend_reconstruction/seed_data.sql`
- Current Flutter codebase with ADR-007 implementation

### **No External Dependencies**
- No third-party services need reconfiguration
- No payment processing setup required
- No external API keys need updating

---

## 📞 **Emergency Contacts & Resources**

### **If Issues Arise**
1. **Supabase Documentation**: https://supabase.com/docs
2. **Supabase Discord**: https://discord.supabase.com
3. **Flutter Supabase Package**: https://pub.dev/packages/supabase_flutter

### **Critical Files to Preserve**
- ADR-007 implementation in Flutter codebase
- Test files validating 3-slot offset fix
- Environment configuration templates

---

**Last Updated**: June 20, 2025  
**Next Review**: After successful reconstruction  
**Responsible**: Development Team
