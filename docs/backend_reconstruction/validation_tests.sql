-- =====================================================
-- VALIDATION TESTS FOR POSTGRESQL FIXES
-- =====================================================
-- Date: June 20, 2025
-- Purpose: Validate that both PostgreSQL fixes work correctly
-- Run these tests AFTER executing complete_database_schema.sql

-- =====================================================
-- TEST 1: VERIFY EXTENSIONS AND SCHEMA
-- =====================================================

-- Check that btree_gist extension is installed
SELECT 
  extname as extension_name,
  extversion as version
FROM pg_extension 
WHERE extname = 'btree_gist';
-- Expected: Should return 'btree_gist' with version number

-- Check that all required tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('profiles', 'pitch_settings', 'bookings', 'time_slots')
ORDER BY table_name;
-- Expected: Should return all 4 tables

-- Check that triggers exist
SELECT trigger_name, event_object_table 
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
  AND trigger_name IN ('set_booking_date_trigger', 'booking_slot_status_trigger')
ORDER BY trigger_name;
-- Expected: Should return both triggers

-- =====================================================
-- TEST 2: BOOKING DATE TRIGGER VALIDATION
-- =====================================================

-- Create a test user (replace with actual user ID from auth.users)
-- Note: This assumes you have a test user created through Flutter app
-- INSERT INTO auth.users (id, email) VALUES ('test-user-uuid', '<EMAIL>');

-- Test booking creation with automatic booking_date setting
INSERT INTO bookings (
  user_id,
  pitch_id,
  slot_start_time,
  slot_end_time,
  status
) VALUES (
  '00000000-0000-0000-0000-000000000001', -- Replace with actual user ID
  1,
  '2025-06-21 14:00:00+00',
  '2025-06-21 15:00:00+00',
  'confirmed'
);

-- Verify booking_date was set correctly by trigger
SELECT 
  id,
  booking_date,
  slot_start_time::DATE as expected_date,
  CASE 
    WHEN booking_date = slot_start_time::DATE THEN 'PASS'
    ELSE 'FAIL'
  END as trigger_test_result
FROM bookings 
WHERE user_id = '00000000-0000-0000-0000-000000000001';
-- Expected: trigger_test_result should be 'PASS'

-- =====================================================
-- TEST 3: OVERLAPPING BOOKING PREVENTION
-- =====================================================

-- Test 3a: Try to create overlapping booking for same user (should FAIL)
DO $$
BEGIN
  BEGIN
    INSERT INTO bookings (
      user_id,
      pitch_id,
      slot_start_time,
      slot_end_time,
      status
    ) VALUES (
      '00000000-0000-0000-0000-000000000001', -- Same user
      1,
      '2025-06-21 14:30:00+00', -- Overlapping with previous booking
      '2025-06-21 15:30:00+00',
      'confirmed'
    );
    
    RAISE NOTICE 'ERROR: Overlapping booking was allowed (should have been prevented)';
  EXCEPTION 
    WHEN exclusion_violation THEN
      RAISE NOTICE 'SUCCESS: Overlapping booking correctly prevented';
  END;
END;
$$;

-- =====================================================
-- TEST 4: DOUBLE BOOKING PREVENTION
-- =====================================================

-- Test 4a: Try to book exact same slot with different user (should FAIL)
DO $$
BEGIN
  BEGIN
    INSERT INTO bookings (
      user_id,
      pitch_id,
      slot_start_time,
      slot_end_time,
      status
    ) VALUES (
      '00000000-0000-0000-0000-000000000002', -- Different user
      1,
      '2025-06-21 14:00:00+00', -- Exact same slot
      '2025-06-21 15:00:00+00',
      'confirmed'
    );
    
    RAISE NOTICE 'ERROR: Double booking was allowed (should have been prevented)';
  EXCEPTION 
    WHEN exclusion_violation THEN
      RAISE NOTICE 'SUCCESS: Double booking correctly prevented';
  END;
END;
$$;

-- =====================================================
-- TEST 5: VALID BOOKING SCENARIOS
-- =====================================================

-- Test 5a: Create non-overlapping booking for same user (should SUCCEED)
INSERT INTO bookings (
  user_id,
  pitch_id,
  slot_start_time,
  slot_end_time,
  status
) VALUES (
  '00000000-0000-0000-0000-000000000001', -- Same user
  1,
  '2025-06-21 16:00:00+00', -- Non-overlapping time
  '2025-06-21 17:00:00+00',
  'confirmed'
);

-- Test 5b: Create booking for different pitch (should SUCCEED)
INSERT INTO bookings (
  user_id,
  pitch_id,
  slot_start_time,
  slot_end_time,
  status
) VALUES (
  '00000000-0000-0000-0000-000000000001', -- Same user
  2, -- Different pitch
  '2025-06-21 14:00:00+00', -- Same time as first booking but different pitch
  '2025-06-21 15:00:00+00',
  'confirmed'
);

-- Verify all valid bookings were created
SELECT 
  id,
  user_id,
  pitch_id,
  slot_start_time,
  slot_end_time,
  booking_date,
  status
FROM bookings 
WHERE user_id = '00000000-0000-0000-0000-000000000001'
ORDER BY slot_start_time;
-- Expected: Should show 3 bookings for the test user

-- =====================================================
-- TEST 6: ADR-007 SLOT STATUS INTEGRATION
-- =====================================================

-- Generate time slots for test date
SELECT * FROM generate_time_slots(1, '2025-06-21', true);

-- Verify that booked slots are marked correctly
SELECT 
  ts.id,
  ts.slot_date,
  ts.start_time,
  ts.end_time,
  ts.is_booked,
  ts.booking_id,
  b.status as booking_status
FROM time_slots ts
LEFT JOIN bookings b ON ts.booking_id = b.id
WHERE ts.slot_date = '2025-06-21' 
  AND ts.pitch_id = 1
ORDER BY ts.start_time;
-- Expected: Slots at 14:00 and 16:00 should show is_booked = true

-- =====================================================
-- TEST 7: BOOKING CANCELLATION
-- =====================================================

-- Test cancellation updates slot status
UPDATE bookings 
SET status = 'cancelledByUser'
WHERE user_id = '00000000-0000-0000-0000-000000000001'
  AND slot_start_time = '2025-06-21 16:00:00+00';

-- Verify slot was marked as available
SELECT 
  ts.start_time,
  ts.is_booked,
  ts.booking_id,
  b.status as booking_status
FROM time_slots ts
LEFT JOIN bookings b ON ts.booking_id = b.id
WHERE ts.slot_date = '2025-06-21' 
  AND ts.pitch_id = 1
  AND ts.start_time = '16:00:00';
-- Expected: is_booked should be false, booking_id should be null

-- =====================================================
-- CLEANUP TEST DATA
-- =====================================================

-- Remove test bookings
DELETE FROM bookings WHERE user_id IN (
  '00000000-0000-0000-0000-000000000001',
  '00000000-0000-0000-0000-000000000002'
);

-- Remove test time slots
DELETE FROM time_slots WHERE slot_date = '2025-06-21';

-- =====================================================
-- FINAL VALIDATION SUMMARY
-- =====================================================

DO $$
BEGIN
  RAISE NOTICE '=== VALIDATION COMPLETE ===';
  RAISE NOTICE 'If you see this message and no errors above, all fixes are working correctly:';
  RAISE NOTICE '✅ btree_gist extension installed';
  RAISE NOTICE '✅ booking_date trigger working';
  RAISE NOTICE '✅ Overlapping booking prevention working';
  RAISE NOTICE '✅ Double booking prevention working';
  RAISE NOTICE '✅ ADR-007 slot status integration working';
  RAISE NOTICE '✅ Booking cancellation working';
  RAISE NOTICE '========================';
END;
$$;
