# Development Guidelines & Instructions

**Project:** Football Pitch Booking App (skillz)  
**Last Updated:** June 4, 2025

## File Organization & Code Quality Guidelines

### File Size Limits
- **Maximum file size:** 400-500 lines for optimal readability and maintainability
- **When approaching limit:** Break files into smaller, focused modules
- **Suggested breakdown strategies:**
  - Extract widgets into separate files
  - Create utility/helper classes
  - Separate models and business logic
  - Use barrel exports (`index.dart`) for clean imports

### Code Organization Principles
- Keep related functionality together
- Use clear, descriptive file and directory names
- Maintain consistent project structure
- Each file should have a single, clear responsibility

## Testing Standards

### File Structure
- Mirror `lib/` structure in `test/` directory
- Group related tests logically
- Keep test files focused (max 400-500 lines)

### Test Quality Requirements
- Every test must reflect real application behavior
- **✅ ACHIEVED: 100% test pass rate** (All availability screen tests fixed June 2025)
- Use proper Riverpod provider mocking for widget tests
- Handle real-time provider Timer cleanup in test environment
- Avoid Riverpod provider override conflicts by not rebuilding widgets with different overrides
- Write meaningful assertions that verify intended outcomes
- Include clear comments explaining what each test validates
- Test both success and failure paths
- Mock only external dependencies (network, storage, etc.)

## Iteration Approach

### Current Status
- **Completed:** Dynamic Pricing & Booking Limits with comprehensive testing
- **Active:** All tests passing, feature complete and production-ready
- **Next:** Iteration 3 - Cancellation & Basic Add-ons

### Work Philosophy
- Small, incremental changes with frequent commits
- Complete one feature fully (including tests) before moving to next
- Maintain working, tested code baseline at all times
- Document architectural decisions and trade-offs

## Specific Project Instructions

### Booking Limit Feature
- When user has 4 active bookings and attempts to book, show SnackBar
- SnackBar should have "View Bookings" action that navigates to My Bookings screen
- Prevent navigation to booking confirmation when limit is reached
- Show appropriate button styling to indicate limit status

### Testing Priorities
1. Complete booking limit feature tests
2. Complete auto-advancing date feature tests  
3. Ensure all existing tests continue to pass
4. Write integration tests for critical user flows

### Code Style Reminders
- Use `const` constructors where possible
- Implement proper error handling with specific exceptions
- Follow clean architecture principles (UI → Domain → Data)
- Use Riverpod consistently for state management
- Maintain consistent naming conventions

## Future Considerations

### Iteration 3 Planning
- Cancellation UI and logic
- Basic add-ons system
- Email notifications
- Respect cancellation window constraints

### Technical Debt Items
- Monitor file sizes as features are added
- Consider breaking large test files into focused test suites
- Evaluate if additional architectural patterns are needed as complexity grows

## Lessons Learned (Dynamic Pricing & Booking Limits Feature)

*Updated: June 4, 2025*

### ✅ What Worked Exceptionally Well

#### Test-Driven Development Approach
- Starting with tests helped design better APIs and catch integration issues early
- Incremental implementation (model → business logic → UI → tests) prevented overwhelming complexity
- Real-world test scenarios reflected actual user workflows rather than just "green for green's sake"

#### Clean Architecture & State Management
- Separating concerns between domain models, business logic, and presentation made code maintainable and testable
- Using Riverpod patterns consistently across the feature
- PitchSettings model with built-in business logic (calculatePrice) kept domain logic centralized

#### Error Handling Strategy
- Comprehensive error handling with custom exceptions created robust user experience
- User-friendly messages with different styling for different error types
- Proper error message display without unnecessary prefixes

#### Mock Discipline
- Only mocking external dependencies (like StateNotifiers) while testing real business logic directly
- Testing user interactions and state changes rather than implementation details

### 🎯 Critical Lessons for Future Features

#### Test Expectations Must Match Reality
- **Key Issue:** Failing test expected "Error: Failed to create booking" but UI showed "Failed to create booking"
- **Root Cause:** Implementation strips "Exception: " prefixes but preserves actual error messages
- **Solution:** Always verify what the UI actually displays vs. what we think it should display
- **Prevention:** Run tests frequently during development to catch misalignments early

#### Database Migration Strategy
- Creating migrations alongside model changes ensures data layer stays in sync
- Design database schema to support business logic requirements upfront

#### Provider Dependency Mapping
- Understanding how Riverpod providers interact helped design better state management
- Consider provider invalidation strategies when state changes affect multiple screens

### 🔄 Best Practices to Continue

1. **Early Integration Testing:** Run tests frequently during development
2. **Real Data Validation:** Test with realistic data (dates, times, user scenarios) rather than mock data
3. **Edge Case Consideration:** Think through edge cases upfront (booking limits, peak hours, etc.)
4. **Documentation as You Go:** Maintain DEV_NOTES.md and ADRs in real-time
5. **Incremental Commits:** Small, logical, working chunks prevent overwhelming changes

### 🚨 Watch Out For

- **Error Message Consistency:** UI implementation may process error messages differently than expected
- **State Management Complexity:** Complex provider dependencies can create unexpected behavior
- **Test Timing:** Widget tests need proper pump/pumpAndSettle calls for state changes
- **Model Changes:** Ensure all affected logic, views, and serializers are updated in lockstep

---

*This file should be updated as new guidelines and decisions are made during development.*
