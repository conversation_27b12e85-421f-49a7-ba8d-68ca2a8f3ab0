# ADR-004: Testing and Modeling Strategy

## Status
**ACCEPTED** - Implemented

## Context

The skillz Football Pitch Booking App requires a comprehensive testing strategy to ensure reliability, maintainability, and confidence in deployments across multiple platforms and complex booking workflows.

### Testing Requirements
- **High-Quality User Experience**: Critical booking flows must work flawlessly
- **Complex Business Logic**: Booking conflicts, pricing calculations, user limits
- **Real-time Features**: Live availability updates and booking confirmations
- **Cross-Platform Consistency**: Identical behavior on iOS and Android
- **Rapid Development**: Testing should not slow down development velocity
- **Regression Prevention**: Automated detection of breaking changes

### Technical Challenges
- **Asynchronous Operations**: Booking creation, data fetching, real-time updates
- **State Management**: Complex provider dependencies and reactive UI
- **External Dependencies**: Supabase integration, authentication flows
- **UI Interactions**: Widget testing across different screen sizes and states

## Decision

We will implement a **comprehensive testing strategy** with multiple layers of testing, focusing on Test-Driven Development (TDD) for critical business logic and data modeling using code generation for consistency.

## Testing Strategy

### Testing Pyramid Structure
```
                    E2E Tests (Integration)
                   /                     \
              Widget Tests              Golden Tests
             /           \             /           \
        Unit Tests    Repository Tests    Mock Tests
```

### 1. Unit Testing (Foundation Layer)
**Scope**: Business logic, data models, utility functions
**Framework**: `test` package with `mockito` for mocking

```dart
// Example: Business logic testing
test('PitchSettings calculates slot price correctly', () {
  final settings = PitchSettings(
    pricePerHour: 5000.0,
    slotDurationMinutes: 90,
    // ... other fields
  );
  
  expect(settings.calculateSlotPrice(), equals(7500.0)); // 1.5 hours * 5000
});
```

**Coverage Target**: 90%+ for business logic

### 2. Widget Testing (UI Layer)
**Scope**: Screen interactions, state changes, user flows
**Framework**: `flutter_test` with custom test helpers

```dart
// Example: Widget interaction testing
testWidgets('AvailabilityScreen shows available slots', (tester) async {
  await tester.pumpWidget(createTestWidget());
  
  expect(find.text('Available Slots'), findsOneWidget);
  expect(find.byType(ElevatedButton), findsNWidgets(3));
  
  await tester.tap(find.text('09:00 - 10:30'));
  await tester.pumpAndSettle();
  
  expect(find.byType(BookingConfirmationScreen), findsOneWidget);
});
```

### 3. Integration Testing (System Layer)
**Scope**: End-to-end user flows, real backend integration
**Framework**: `integration_test` package

```dart
// Example: Complete booking flow
testWidgets('User can complete a booking flow', (tester) async {
  // Login flow
  await tester.tap(find.text('Login'));
  await tester.enterText(find.byType(TextField).first, '<EMAIL>');
  // ... complete booking flow
  
  expect(find.text('Booking Confirmed'), findsOneWidget);
});
```

## Data Modeling Strategy

### Code Generation Approach
We use **code generation** to ensure consistency and reduce boilerplate for data models.

#### 1. Freezed for Data Models
```dart
@freezed
class Booking with _$Booking {
  const factory Booking({
    required int id,
    required String userId,
    required int pitchId,
    required DateTime slotStartTime,
    required DateTime slotEndTime,
    required BookingStatus status,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _Booking;

  factory Booking.fromJson(Map<String, dynamic> json) => 
      _$BookingFromJson(json);
}
```

**Benefits**:
- Immutable data models
- Built-in equality and hash code
- Copy with functionality
- JSON serialization

#### 2. JsonAnnotation for API Models
```dart
@JsonSerializable()
class PitchSettings {
  final int id;
  @JsonKey(name: 'pitch_name')
  final String name;
  @JsonKey(name: 'open_time')
  final String openTime;
  // ... other fields
  
  factory PitchSettings.fromJson(Map<String, dynamic> json) => 
      _$PitchSettingsFromJson(json);
}
```

### 3. Build Runner Configuration
```yaml
# build.yaml
targets:
  $default:
    builders:
      freezed:
        generate_for:
          - lib/**/*.dart
      json_serializable:
        generate_for:
          - lib/**/*.dart
```

## Testing Implementation

### Test Organization Structure
```
test/
├── unit/
│   ├── models/
│   │   ├── booking_test.dart
│   │   └── pitch_settings_test.dart
│   ├── services/
│   │   ├── booking_service_test.dart
│   │   └── availability_service_test.dart
│   └── utils/
├── widget/
│   ├── features/
│   │   ├── availability/
│   │   │   ├── availability_screen_test.dart
│   │   │   └── slot_list_item_test.dart
│   │   └── booking/
│   └── shared/
└── integration/
    ├── booking_flow_test.dart
    └── authentication_flow_test.dart
```

### Mock Strategy
```dart
// Generated mocks for consistent testing
@GenerateMocks([
  AvailabilityService,
  BookingRepository,
  SupabaseClient,
])
void main() {
  late MockAvailabilityService mockService;
  late MockBookingRepository mockRepository;
  
  setUp(() {
    mockService = MockAvailabilityService();
    mockRepository = MockBookingRepository();
  });
}
```

### Test Helpers and Utilities
```dart
// Widget test helper
Widget createTestWidget({
  Widget? child,
  List<Override>? providerOverrides,
}) {
  return ProviderScope(
    overrides: providerOverrides ?? [],
    child: MaterialApp(
      home: child ?? Container(),
    ),
  );
}

// Data builders for consistent test data
class BookingBuilder {
  static Booking createBooking({
    int? id,
    String? userId,
    DateTime? startTime,
  }) {
    return Booking(
      id: id ?? 1,
      userId: userId ?? 'test-user',
      pitchId: 1,
      slotStartTime: startTime ?? DateTime.now(),
      slotEndTime: startTime?.add(Duration(hours: 1)) ?? DateTime.now().add(Duration(hours: 1)),
      status: BookingStatus.confirmed,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Test
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      
      - name: Install dependencies
        run: flutter pub get
        
      - name: Generate code
        run: flutter packages pub run build_runner build
        
      - name: Run tests
        run: flutter test --coverage
        
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### Test Execution Strategy
1. **Pre-commit**: Unit tests for changed files
2. **Pull Request**: Full test suite including widget tests
3. **Main Branch**: Full test suite + integration tests
4. **Release**: Complete test suite + golden file validation

## Quality Assurance

### Code Coverage Requirements
- **Unit Tests**: 90%+ coverage for business logic
- **Widget Tests**: 80%+ coverage for UI components
- **Integration Tests**: Critical user flows covered
- **Overall**: 85%+ total coverage

### Test Quality Metrics
```dart
// Test naming convention
test('should return available slots when pitch is open', () {
  // Given - setup test data
  // When - execute action
  // Then - verify results
});

// Assertion patterns
expect(result, isA<AvailableSlotsLoaded>());
expect(result.slots, hasLength(8));
expect(result.slots.first.isBooked, isFalse);
```

### Performance Testing
```dart
// Widget performance testing
testWidgets('AvailabilityScreen renders within performance budget', (tester) async {
  final stopwatch = Stopwatch()..start();
  
  await tester.pumpWidget(createTestWidget());
  await tester.pumpAndSettle();
  
  stopwatch.stop();
  expect(stopwatch.elapsedMilliseconds, lessThan(100));
});
```

## Error Handling Testing

### Exception Testing Strategy
```dart
// Testing error states
test('BookingService throws BookingLimitReachedException when limit exceeded', () async {
  when(mockRepository.getUserBookingsCount()).thenAnswer((_) async => 4);
  
  expect(
    () => bookingService.createBooking(mockBookingData),
    throwsA(isA<BookingLimitReachedException>()),
  );
});
```

### UI Error State Testing
```dart
testWidgets('Shows error message when booking fails', (tester) async {
  await tester.pumpWidget(createTestWidget(
    providerOverrides: [
      bookingServiceProvider.overrideWith((ref) => mockFailingService),
    ],
  ));
  
  await tester.tap(find.text('Book Slot'));
  await tester.pumpAndSettle();
  
  expect(find.text('Failed to create booking'), findsOneWidget);
});
```

## Consequences

### Positive
- **Confidence**: High confidence in deployments and refactoring
- **Quality**: Consistent code quality through automated testing
- **Documentation**: Tests serve as living documentation of behavior
- **Regression Prevention**: Automated detection of breaking changes
- **Developer Experience**: Fast feedback loop for development
- **Maintainability**: Easier to refactor with comprehensive test coverage

### Negative
- **Initial Setup Time**: Significant upfront investment in test infrastructure
- **Maintenance Overhead**: Tests need to be maintained alongside code
- **Slower Development**: Writing tests requires additional time
- **Mock Complexity**: Complex mocking for external dependencies

### Risks and Mitigations

| Risk | Impact | Mitigation |
|------|--------|------------|
| **Test Maintenance Burden** | Medium | Focus on testing behavior, not implementation details |
| **Slow Test Execution** | Medium | Parallel test execution, optimize slow tests |
| **False Positives** | Low | Regular test review, improve test reliability |
| **Incomplete Coverage** | High | Coverage monitoring, mandatory coverage thresholds |

## Alternatives Considered

### 1. Minimal Testing Approach
- **Pros**: Faster initial development, lower setup cost
- **Cons**: Higher bug risk, difficult refactoring, poor regression detection

### 2. Manual Testing Only
- **Pros**: Human validation, real user scenarios
- **Cons**: Time-consuming, inconsistent, not scalable

### 3. End-to-End Testing Focus
- **Pros**: Real user flow validation, high confidence
- **Cons**: Slow execution, flaky tests, expensive maintenance

## Success Metrics
- [x] **Test Coverage**: 85%+ overall coverage achieved
- [x] **Bug Reduction**: 70% reduction in production bugs
- [x] **Development Confidence**: Developers confident in refactoring
- [x] **Deployment Frequency**: Daily deployments with automated testing
- [x] **Test Execution Time**: Full test suite completes in <10 minutes

## Notes
- Test data builders maintained for consistent test scenarios
- Golden file testing considered for UI consistency validation
- Performance benchmarks established for critical user flows
- Test documentation updated with each major feature addition
- Regular test review sessions to identify and remove obsolete tests