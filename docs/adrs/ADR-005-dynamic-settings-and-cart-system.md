# ADR-005: Dynamic Pitch Settings and Multi-Booking Cart System

## Status
**SUPERSEDED** - Replaced with simplified approach (see DEV_NOTES.md 2025-06-04)

**Decision**: After careful analysis, the team opted for a minimal viable enhancement instead of the full cart system. This approach solves core problems (pricing, dynamic configuration) without architectural complexity.

## Context

The current booking system has several limitations that prevent it from scaling to production requirements:

### Current System Limitations
1. **Hardcoded Configuration**: Booking limits (4), pricing (free), slot durations are hardcoded
2. **Single Booking Flow**: Users can only book one slot at a time, requiring multiple confirmation flows
3. **No Pricing Support**: System assumes free bookings with no payment infrastructure
4. **Static Settings**: No admin interface to configure pitch-specific settings
5. **Poor Multi-Booking UX**: No shopping cart experience for users who want multiple slots

### Business Requirements
- **Pricing is Essential**: The app must support paid bookings
- **Multi-Booking Expected**: Users frequently book multiple consecutive or separate time slots
- **Admin Configuration**: Pitch owners need to set prices, hours, slot durations, and booking limits
- **Scalability**: Support multiple pitches with different configurations

## Decision

We will implement a **Phased Dynamic Settings and Cart System** with the following components:

### Phase 1: Dynamic Pitch Settings Foundation
**Goal**: Replace hardcoded values with database-driven configuration

**Components**:
1. **PitchSettings Model**: Comprehensive settings for each pitch
   - `pricePerHour`: Hourly rate for calculations
   - `slotDurationMinutes`: Configurable slot duration (60, 90, 120 minutes)
   - `maxBookingsPerUser`: Dynamic booking limits per pitch
   - `operatingHours`: Open/close times
   - `availableDays`: Days of operation
   
2. **PitchSettingsRepository**: Data layer for settings management
   - CRUD operations for settings
   - Real-time updates from admin changes
   - Caching for performance

3. **Enhanced Database Schema**: `pitch_settings` table with comprehensive configuration

### Phase 2: Multi-Booking Cart System
**Goal**: Transform single booking flow into shopping cart experience

**Components**:
1. **BookingItem Model**: Represents potential bookings before confirmation
   - Calculated pricing based on dynamic settings
   - Conflict detection capabilities
   - Duration and time range calculations

2. **BookingCart State Management**: Riverpod-based cart operations
   - Add/remove items with validation
   - Conflict detection across cart items
   - Dynamic limit enforcement per pitch
   - Price calculations and totals

3. **Enhanced UI Flow**:
   - AvailabilityScreen: "Add to Cart" buttons instead of direct booking
   - BookingCartScreen: Review cart with pricing breakdown
   - Checkout flow preparation for payment integration

### Phase 3: Integration and Optimization
**Goal**: Connect systems and prepare for production

**Components**:
1. Real-time settings synchronization
2. Payment system integration points
3. Performance optimization and caching
4. Comprehensive testing suite

## Technical Design

### Architecture Principles
- **Clean Architecture**: Maintain separation between Domain, Application, and Data layers
- **Backward Compatibility**: Existing single booking flow continues during transition
- **Incremental Migration**: Phase implementation to avoid big-bang deployment
- **Testability**: Each component designed for isolated testing

### Data Models

```dart
// Core settings model
class PitchSettings {
  final String pitchId;
  final double pricePerHour;
  final int slotDurationMinutes;
  final int maxBookingsPerUser;
  final TimeOfDay openTime;
  final TimeOfDay closeTime;
  // ... other configuration
}

// Cart item model
class BookingItem {
  final String tempId;
  final String pitchId;
  final DateTime slotStartTime;
  final DateTime slotEndTime;
  final double slotPrice; // Calculated from settings
  final PitchSettings pitchSettings;
}
```

### State Management Strategy
- **Riverpod Providers**: Settings, cart state, and booking operations
- **Real-time Streams**: Admin setting changes propagate immediately
- **Optimistic Updates**: Cart operations provide immediate feedback
- **Error Boundaries**: Comprehensive exception handling at each layer

## Consequences

### Positive
- **Scalable Pricing**: Support for complex pricing models and payment integration
- **Better UX**: Shopping cart pattern familiar to users
- **Admin Flexibility**: Dynamic configuration without code deployments
- **Production Ready**: Supports real business requirements
- **Maintainable**: Clean separation of concerns and testable components

### Negative
- **Increased Complexity**: More models, providers, and state to manage
- **Development Time**: Significant implementation effort across multiple phases
- **Migration Risk**: Changes to core booking flow require careful testing
- **Performance Considerations**: Additional database queries and real-time subscriptions

### Risks and Mitigations

| Risk | Impact | Mitigation |
|------|--------|------------|
| **Application Bloat** | High complexity, hard to maintain | Phased implementation, strict interface boundaries |
| **Migration Issues** | Broken booking flow | Maintain backward compatibility, comprehensive testing |
| **Performance Degradation** | Slow user experience | Implement caching, optimize queries, lazy loading |
| **Over-engineering** | Excessive complexity for user base | Start with minimal viable implementation, iterate |

## Alternatives Considered

### 1. Enhanced Single Booking
- **Pros**: Minimal complexity increase, faster implementation
- **Cons**: Doesn't solve multi-booking UX, still requires dynamic settings

### 2. Hybrid Approach
- **Pros**: Gradual evolution, less risky
- **Cons**: Temporary complexity, multiple code paths to maintain

### 3. External Cart Service
- **Pros**: Specialized solution, reduced app complexity
- **Cons**: Additional dependencies, network latency, increased costs

## Implementation Plan

### Week 1: Foundation
- [ ] Create PitchSettings model and repository
- [ ] Set up database schema and migrations
- [ ] Implement basic settings providers
- [ ] Unit tests for settings logic

### Week 2: Cart System
- [ ] Implement BookingItem model
- [ ] Create cart state management
- [ ] Build cart UI components
- [ ] Integration tests for cart operations

### Week 3: Integration
- [ ] Connect cart to existing booking flow
- [ ] Implement real-time updates
- [ ] Performance optimization
- [ ] End-to-end testing

### Week 4: Polish and Documentation
- [ ] User acceptance testing
- [ ] Documentation updates
- [ ] Performance monitoring
- [ ] Deployment preparation

## Success Metrics
- [ ] **Functionality**: Users can add multiple items to cart and checkout
- [ ] **Performance**: Cart operations complete within 200ms
- [ ] **Reliability**: 99%+ success rate for booking operations
- [ ] **Usability**: Reduced booking completion time for multi-slot bookings
- [ ] **Maintainability**: Test coverage >80% for new components

## Notes
- Admin interface design deferred to separate ADR
- Payment integration points identified but implementation separate
- Real-time updates may require WebSocket consideration for high-traffic scenarios
