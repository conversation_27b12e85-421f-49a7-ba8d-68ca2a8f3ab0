# ADR-007: Database-Centric Slot Management and Status Calculation

**Status:** � **Fast-Track Implementation** (0% Complete)  
**Date:** 2025-06-18  
**Deciders:** Development Team  
**Technical Story:** URGENT: Fix 3-slot offset bug via rapid database-centric slot management implementation  
**Implementation Status:** ⚡ **Fast-Track - Same Day Implementation**

## Context and Problem Statement

The current availability system calculates slot booking status client-side by comparing booking records with generated time slots. This approach has led to several critical issues:

### Current Problems:
1. **Incorrect Slot Status Display:** Slots are being marked as booked with a 3-slot offset error
2. **Client-Side Complexity:** Complex DateTime comparison logic prone to timezone/precision errors
3. **Performance Overhead:** Recalculating slot status on every render/fetch
4. **Inconsistency Risk:** Different clients might calculate different results
5. **Race Conditions:** Slot status might be stale during booking operations
6. **Debugging Difficulty:** Hard to trace slot status calculation issues

### Current Implementation Issues:
```dart
// Problematic client-side calculation
final isBooked = bookings.any((booking) {
  final startMatch = booking.slotStartTime.isAtSameMomentAs(currentSlotStart);
  final endMatch = booking.slotEndTime.isAtSameMomentAs(currentSlotEnd);
  return startMatch && endMatch; // Prone to timezone/precision errors
});
```

### Requirements:
- **URGENT Bug Fix:** 100% reliable slot booking status display (fix 3-slot offset immediately)
- **Same-Day Implementation:** Database-centric approach implemented within hours
- **Zero Downtime:** Live system remains operational during migration
- **Immediate Testing:** Validate fix works correctly today
- **Simple Rollback:** Easy revert if issues found
- **Maintain Real-Time:** Keep current real-time update capabilities

## Decision Drivers

- **Data Accuracy:** Critical to prevent user confusion and lost bookings
- **System Reliability:** Database-managed state is more reliable than client calculations
- **Performance:** Pre-calculated slot status reduces client processing
- **Maintainability:** Centralized slot logic easier to debug and extend
- **Scalability:** Database operations scale better than client calculations
- **Development Velocity:** Simpler client code allows faster feature development

## Considered Options

### Option 1: Fix Current Client-Side Calculation
**Pros:**
- Minimal changes required
- No database schema changes
- Quick fix

**Cons:**
- Doesn't address fundamental architectural issues
- Still prone to timezone/precision errors
- Maintains complex client-side logic
- Performance overhead remains

### Option 2: Hybrid Approach
**Pros:**
- Gradual migration possible
- Can maintain backward compatibility

**Cons:**
- Increases system complexity
- Potential for inconsistencies during transition
- Doesn't fully solve the core issues

### Option 3: Full Database-Centric Slot Management (Chosen)
**Pros:**
- Database as single source of truth
- Eliminates client-side calculation errors
- Better performance and scalability
- Easier to maintain and debug
- Enables advanced features (reservations, pricing tiers)
- Atomic operations ensure consistency

**Cons:**
- Requires significant refactoring
- Database schema changes needed
- Migration complexity

## Decision

**We will implement a full database-centric slot management system** where:

1. **Time slots are stored as database records** with pre-calculated status
2. **Database triggers automatically update slot status** when bookings change
3. **Client applications simply fetch and display** slot data
4. **Database functions generate slots** based on pitch settings

## Fast-Track Implementation Strategy

**Target Timeline:** 2-4 hours total implementation time

### Hour 1: Database Setup (30 minutes)
- Create `time_slots` table with essential fields
- Create basic slot generation function
- Create booking status update trigger

### Hour 2: Data Migration (30 minutes)  
- Generate slots for next 7 days for all pitches
- Sync existing booking statuses
- Validate data integrity

### Hour 3: Flutter Updates (60 minutes)
- Create simplified database provider
- Update TimeSlotInfo model with database fields
- Add feature flag for switching between systems

### Hour 4: Testing & Rollout (30 minutes)
- Test both systems side-by-side
- Enable for limited users first
- Full rollout if validation passes

### Emergency Rollback (5 minutes if needed)
- Feature flag to revert to current system
- No database changes affect existing functionality

## New Database Schema

### Core Tables
```sql
-- Main slot storage table
CREATE TABLE time_slots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  pitch_id UUID REFERENCES pitches(id) ON DELETE CASCADE,
  slot_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_booked BOOLEAN DEFAULT FALSE,
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  price_per_hour DECIMAL(10,2) NOT NULL,
  is_available BOOLEAN DEFAULT TRUE, -- For manual override
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_time_slots_pitch_date ON time_slots(pitch_id, slot_date);
CREATE INDEX idx_time_slots_available ON time_slots(pitch_id, slot_date, is_available);
CREATE INDEX idx_time_slots_booking ON time_slots(booking_id);
CREATE UNIQUE INDEX idx_time_slots_unique ON time_slots(pitch_id, slot_date, start_time);
```

### Database Functions
```sql
-- Generate slots for a pitch/date combination
CREATE OR REPLACE FUNCTION generate_time_slots(
  p_pitch_id UUID,
  p_date DATE
) RETURNS INTEGER; -- Returns number of slots created

-- Update slot availability based on time
CREATE OR REPLACE FUNCTION update_past_slot_availability()
RETURNS INTEGER; -- Returns number of slots updated

-- Bulk generate slots for multiple days
CREATE OR REPLACE FUNCTION generate_slots_bulk(
  p_pitch_id UUID,
  p_start_date DATE,
  p_end_date DATE
) RETURNS INTEGER;
```

### Triggers
```sql
-- Auto-update slot status on booking changes
CREATE TRIGGER booking_slot_status_trigger
  AFTER INSERT OR UPDATE OR DELETE ON bookings
  FOR EACH ROW EXECUTE FUNCTION update_slot_booking_status();

-- Update timestamps
CREATE TRIGGER time_slots_updated_at_trigger
  BEFORE UPDATE ON time_slots
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## New Flutter Architecture

### Simplified Provider
```dart
class DatabaseSlotProvider extends StateNotifier<AsyncValue<List<TimeSlotInfo>>> {
  // Simple database fetch - no calculations
  Future<void> loadSlots(String pitchId, DateTime date) async {
    final slots = await supabase
        .from('time_slots')
        .select('*')
        .eq('pitch_id', pitchId)
        .eq('slot_date', date.toIso8601String().split('T')[0])
        .order('start_time');
    
    state = AsyncValue.data(
      slots.map((json) => TimeSlotInfo.fromDatabaseJson(json)).toList()
    );
  }
}
```

### Updated Models
```dart
class TimeSlotInfo {
  final String id;
  final DateTime startTime;
  final DateTime endTime;
  final bool isBooked;
  final bool isAvailable;
  final String? bookingId;
  final double price;
  
  // Simple factory from database JSON
  factory TimeSlotInfo.fromDatabaseJson(Map<String, dynamic> json) {
    // Direct mapping, no calculations
  }
}
```

## Migration Plan

### Data Migration Strategy
1. **Parallel System Operation:** Run both systems during transition
2. **Slot Data Generation:** Bulk generate slots for existing pitches
3. **Booking Status Sync:** Ensure existing bookings update slot status
4. **Validation Phase:** Compare old vs new system results
5. **Gradual Cutover:** Feature-flag controlled migration
6. **Cleanup Phase:** Remove old calculation logic

### Rollback Strategy
- Maintain old system during migration
- Feature flags for easy system switching
- Database migration scripts with rollback procedures
- Monitoring and alerting for migration issues

### Risk Mitigation
- **Data Loss Prevention:** Comprehensive backups before migration
- **Performance Monitoring:** Track database performance during transition
- **User Impact Minimization:** Off-peak migration scheduling
- **Testing Coverage:** Extensive testing of new system before cutover

## Code Cleanup Plan

### Files to Refactor/Remove
```
lib/features/availability/application/
├── real_time_availability_provider.dart  # Major refactoring
└── availability_service.dart              # Simplification

lib/features/availability/domain/
├── time_slot_info_model.dart              # Update model
└── availability_calculator.dart           # Remove (if exists)

lib/debug/
├── availability_debug_helper.dart         # Update/remove
└── slot_booking_debugger.dart            # Remove after migration

supabase/migrations/
├── create_time_slots_table.sql           # New
├── create_slot_functions.sql             # New
└── migrate_existing_data.sql             # New
```

### Testing Strategy
- **Unit Tests:** Database functions and triggers
- **Integration Tests:** Full slot lifecycle testing
- **Performance Tests:** Database query optimization
- **Migration Tests:** Data consistency validation
- **End-to-End Tests:** Complete user workflows

## Success Metrics

### Functional Metrics
- **Slot Accuracy:** 100% correct slot status display
- **Performance:** <100ms slot loading time
- **Consistency:** 0% discrepancies between clients
- **Reliability:** 99.9%+ availability during migration

### Technical Metrics
- **Code Complexity:** 50% reduction in availability-related code
- **Database Load:** <10% increase in database operations
- **Client Processing:** 80% reduction in client-side calculations
- **Bug Reports:** 90% reduction in slot-related issues

## Long-term Benefits

### Immediate Benefits
- Accurate slot status display
- Simplified client code
- Better performance
- Easier debugging

### Future Capabilities Enabled
- **Dynamic Pricing:** Time-based pricing in database
- **Slot Reservations:** Temporary holds on slots
- **Batch Operations:** Bulk slot management
- **Advanced Analytics:** Database-level slot analytics
- **Third-party Integration:** API-based slot management

## Consequences

### Positive
- **Reliability:** Database-managed state more reliable
- **Performance:** Reduced client processing overhead
- **Maintainability:** Centralized slot logic
- **Scalability:** Better scaling characteristics
- **Feature Velocity:** Easier to add slot-related features

### Negative
- **Migration Complexity:** Significant effort required
- **Database Load:** Increased database operations
- **Schema Changes:** Breaking changes to data model
- **Learning Curve:** Team needs to understand new architecture

### Risks & Mitigation
- **Migration Downtime:** Mitigated by parallel system operation
- **Data Inconsistency:** Mitigated by comprehensive testing
- **Performance Degradation:** Mitigated by proper indexing and monitoring
- **Rollback Complexity:** Mitigated by feature flags and backup strategies

## Timeline

| Phase | Duration | Deliverables |
|-------|----------|-------------|
| Database Setup | 30 minutes | Tables, functions, triggers |
| Data Migration | 30 minutes | Slot generation, booking sync |
| Flutter Updates | 60 minutes | Database provider, feature flags |
| Testing & Rollout | 30 minutes | Validation, gradual deployment |

**Total Implementation Time:** 2-4 hours  
**Emergency Rollback Time:** 5 minutes (feature flag)  
**Target Completion:** Same day (June 18, 2025)

---

## 🔄 IMPLEMENTATION STATUS UPDATE - June 18, 2025

### **Current Status: 75% Complete** ✅

#### ✅ **Completed Work (Same Day)**
1. **Core Architecture Implementation**
   - `DatabaseSlotProvider` class fully implemented with TDD approach
   - `TimeSlotInfo.fromDatabaseJson()` method complete
   - Real-time database streaming functionality working

2. **Provider Integration**
   - `real_time_availability_provider.dart` refactored to use database streaming
   - `real_time_availability_simple.dart` converted from polling to streaming
   - Riverpod ecosystem integration complete
   - Build system generating all required code

3. **Architecture Validation**
   - Client-side slot calculations completely eliminated
   - Database as single source of truth established
   - Real-time updates via `time_slots` table streaming
   - Error handling and fallback mechanisms in place

#### ❌ **Remaining Work (Critical)**
1. **Test Infrastructure** (HIGH PRIORITY)
   - Fix `database_slot_provider_test.dart` syntax and mock issues
   - Complete TDD red-green-refactor cycle
   - Comprehensive test coverage for all provider methods

2. **Integration Validation** (MUST HAVE)
   - End-to-end testing of slot booking accuracy
   - Verify 3-slot offset bug is definitively fixed
   - Real-time streaming performance validation
   - UI integration testing

### **Next Session Priority**
Focus 100% on testing infrastructure to reach completion and validate the 3-slot offset fix.

**Target: 100% completion within 2-3 focused work sessions on testing**

**Related ADRs:** ADR-006 (Real-Time Availability Updates)  
**Supersedes:** Current client-side slot calculation approach  
**References:** Database migration guide, development guidelines
