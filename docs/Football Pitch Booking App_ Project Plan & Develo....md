# **Football Pitch Booking App: Project Plan**

**Last Updated:** May 22, 2025

## **1\. Project Requirements Document (PRD) \- Initial Draft**

### **1.1. Project Vision**

To create a simple, user-friendly mo**Technical Tasks for Iteration 2:**

**Database Schema & Concurrency Control:**
* [ ] Update bookings table schema to support atomic operations:
  * Add `created_at` timestamp with default `now()`
  * Add unique constraint on `(slot_start_time, slot_end_time, pitch_id)` where `status != 'cancelled'`
  * Add `version` field for optimistic locking (optional, for advanced conflict detection)
* [ ] Create Supabase Edge Function for atomic booking creation:
  * Function name: `create_booking_atomic`
  * Checks slot availability within transaction
  * Creates booking record atomically
  * Returns success/failure with specific error codes for different scenarios
* [ ] Implement database-level constraints to prevent double bookings:
  * Unique index on time slots for active bookings
  * Check constraints for valid time ranges
  * RLS policies for secure access

**Real-Time Data Synchronization:**
* [ ] Set up Supabase Realtime subscriptions in Flutter:
  * Subscribe to `bookings` table changes for the selected date/pitch
  * Handle INSERT, UPDATE, DELETE events
  * Filter subscription by date range to optimize performance
* [ ] Create Riverpod providers for real-time availability:
  * `realTimeAvailabilityProvider` - combines static data with real-time updates  
  * `bookingStreamProvider` - streams booking changes from Supabase
  * `timeBasedAvailabilityProvider` - automatically marks past slots as unavailable
* [ ] Implement automatic slot status updates:
  * Timer-based updates for past slots (every minute)
  * Real-time updates for booking changes
  * Optimistic UI updates with rollback on failure

**Race Condition Prevention:**
* [ ] Implement optimistic booking flow:
  * Immediate UI feedback when user clicks "Book"
  * Atomic booking creation on server
  * UI rollback if booking fails due to conflicts
* [ ] Create conflict detection and resolution:
  * Custom exception types: `SlotUnavailableException`, `BookingConflictException`
  * Retry mechanism for transient failures
  * Clear error messages for different conflict scenarios
* [ ] Add booking validation:
  * Client-side validation before server call
  * Server-side validation in Edge Function
  * Double-check slot availability in booking confirmation screen

**UI/UX Implementation:**
* [ ] Update AvailabilityScreen for real-time updates:
  * Loading states for slot status changes
  * Smooth animations for availability changes
  * Pull-to-refresh functionality
  * Auto-refresh when app regains focus
* [ ] Enhance BookingConfirmationScreen:
  * Real-time slot availability checking
  * Timeout handling for booking requests
  * Redirect to availability on slot conflicts
* [ ] Implement booking success/failure handling:
  * Success dialog with options: "Done" / "Book Another"
  * Error handling with specific messages for different failure types
  * Automatic screen refresh after successful booking

**State Management & Caching:**
* [ ] Riverpod provider architecture for real-time data:
  * `availableSlotsProvider` - refreshes based on real-time events
  * `bookingCreationProvider` - manages booking process state
  * `userBookingsProvider` - user's personal bookings with real-time updates
* [ ] Implement intelligent caching:
  * Cache availability data with expiration
  * Invalidate cache on real-time updates
  * Background refresh for recently viewed dates
* [ ] Add connection state management:
  * Handle offline scenarios gracefully
  * Show connection status indicators
  * Queue actions when offline (for future enhancement)

**Testing Strategy:**
* [ ] Unit tests for race condition scenarios:
  * Concurrent booking attempts
  * Time-based availability logic
  * Conflict resolution mechanisms
* [ ] Integration tests for real-time features:
  * Supabase Realtime subscription testing
  * End-to-end booking flow testing
  * Multi-user booking conflict testing
* [ ] Performance testing:
  * Load testing for concurrent bookings
  * Real-time update performance
  * Memory usage with long-running real-time subscriptions

**Error Handling & Logging:**
* [ ] Comprehensive error handling:
  * Network failure recovery
  * Real-time connection drops
  * Booking conflict scenarios
* [ ] Add logging for debugging:
  * Booking attempt logs
  * Real-time event logs
  * Performance metricsweb application that allows customers to easily view availability, book time slots, and manage their bookings for a local football pitch. The app should also provide an admin interface for the owner to manage bookings, availability, and pricing.

### **1.2. Target Audience**

* **Customers:** Individuals or groups looking to rent the football pitch.  
* **Admin (Pitch Owner):** Your brother, who will manage the pitch operations.

### **1.3. Key Features (High-Level User Stories)**

**Customer Facing:**

* As a user, I want to sign up using my email address so I can create an account.  
* As a user, I want to log in with my email and password.  
* As a user, I want to view available time slots for the pitch on a given day/week.  
* As a user, I want to book an available time slot.  
* As a user, I want to select optional add-ons (e.g., match package, training package, individual items) for my booking.  
* As a user, I want to pay for my booking securely using local payment methods (Airtel Money, TNM Mpamba, Bank).  
* As a user, I want to receive a confirmation (email) for my booking.  
* As a user, I want to view my upcoming and past bookings.  
* As a user, I want to cancel my booking within a 4-hour window before the slot time for a refund.  
* As a user, I want to receive a notification (email) for booking cancellations.  
* As a user, I want to be reminded (email) about my upcoming booking.

**Real-Time Availability & Data Consistency:**

* As a user, when I successfully book a time slot, I want to immediately see that slot marked as unavailable when I return to the availability screen.  
* As a user, when another user books a slot while I'm viewing the availability screen, I want to see that slot automatically update to unavailable without having to refresh manually.  
* As a user, when I attempt to book a slot that has just been booked by another user, I want to receive a clear error message and see the updated availability immediately.  
* As a user, I want the system to prevent double bookings by ensuring only one user can successfully book a specific time slot, even if multiple users attempt to book simultaneously.  
* As a user, when the current time passes a slot's start time, I want that slot to automatically become unbookable and show as "Past" or "Unavailable".  
* As a user, when viewing availability, I want to see real-time updates if slots become available due to cancellations by other users.  
* As a user, if I'm in the middle of booking a slot and another user books it first, I want to be informed immediately with an option to select an alternative slot.

**Admin Facing:**

* As an admin, I want to log in securely to an admin panel.  
* As an admin, I want to view all bookings (upcoming, past, cancelled).  
* As an admin, I want to manually add or modify bookings.  
* As an admin, I want to block out time slots for maintenance or private events.  
* As an admin, I want to manage the pricing for slots and additional items.  
* As an admin, I want to manage the list of available additional items/packages.  
* As an admin, I want to configure the cancellation window (e.g., currently 4 hours).  
* As an admin, I want to configure pitch operating hours (currently 05:30 \- 22:30).  
* As an admin, I want to configure booking slot duration (currently 90 minutes).

### **1.4. Non-Functional Requirements**

* **Usability:** Simple, intuitive interface.  
* **Performance:** Fast loading times for availability and booking.  
* **Security:** Secure handling of user data and payments. RLS in Supabase.  
* **Reliability:** Accurate booking and availability information.  
* **Scalability:** (Future consideration) Ability to handle increasing users and bookings.  
* **Cross-Platform:** Works on iOS, Android, and Web.

### **1.5. Data Consistency & Real-Time Requirements**

* **Race Condition Prevention:** The system must prevent double bookings through atomic database operations and proper concurrency control.  
* **Real-Time Updates:** Availability screens should reflect booking changes in near real-time (within 2-3 seconds) across all user sessions.  
* **Time-Based Availability:** Slots should automatically become unavailable once their start time has passed.  
* **Optimistic UI Updates:** UI should provide immediate feedback while server operations are in progress, with rollback capabilities for failed operations.  
* **Conflict Resolution:** Clear user feedback and recovery options when booking conflicts occur.  
* **Data Freshness:** Availability data shown to users should be accurate within acceptable tolerances (≤30 seconds for non-critical updates).

## **2\. Technical Stack & Architecture**

* **Frontend:** Flutter (SDK Version: Latest Stable)  
  * Language: Dart (Latest Stable)  
  * State Management: **Riverpod** (flutter\_riverpod package)  
  * Routing: (Flutter Navigator 2.0 / go\_router \- go\_router is often preferred with Riverpod)  
* **Backend:** Supabase  
  * Database: PostgreSQL  
  * Authentication: Supabase Auth (Email/Password initially, SMS later)  
  * Storage: Supabase Storage (if needed for images of items, etc. \- low priority)  
  * Edge Functions: For payment gateway integrations, complex notifications, atomic booking operations  
  * Realtime: Supabase Realtime for live availability updates  
* **Key Flutter Packages (Initial thoughts):**  
  * supabase\_flutter: For Supabase integration.  
  * flutter\_riverpod: For state management.  
  * riverpod\_annotation (optional, for generator): For Riverpod providers (dev dependency).  
  * riverpod\_generator (optional, for generator): Riverpod code generator (dev dependency).  
  * intl: For date/time formatting.  
  * freezed\_annotation: Core annotations for freezed.  
  * freezed: The freezed code generator (dev dependency).  
  * json\_annotation: Core annotations for json\_serializable.  
  * json\_serializable: For fromJson/toJson generation (dev dependency).  
  * build\_runner: To run code generators (dev dependency).  
  * go\_router: For declarative routing.  
  * (Payment gateway SDKs \- to be researched for Airtel Money, TNM Mpamba, Banks)  
  * flutter\_local\_notifications / firebase\_messaging (for push notifications later)  
  * email\_validator (or similar for input validation)  
* **Target Platforms:** iOS, Android, Web.

### **2.1. Real-Time Architecture & Concurrency Control**

**Database-Level Concurrency Control:**
* **Unique Constraints:** Prevent duplicate bookings at database level
* **Atomic Transactions:** All booking operations wrapped in database transactions  
* **Row-Level Locking:** Temporary locks on slots during booking process
* **Optimistic Locking:** Version fields for conflict detection (optional enhancement)

**Real-Time Data Flow:**
```
User Action → Optimistic UI Update → Server Validation → Database Transaction → Realtime Broadcast → All Clients Update
```

**Supabase Realtime Integration:**
* **Subscription Strategy:** Subscribe to booking changes filtered by date range and pitch
* **Event Handling:** Handle INSERT/UPDATE/DELETE events for bookings table
* **Connection Management:** Automatic reconnection and state recovery
* **Performance Optimization:** Targeted subscriptions to reduce unnecessary updates

**Conflict Resolution Strategy:**
1. **Prevention:** Database constraints and atomic operations
2. **Detection:** Server-side validation before booking creation  
3. **Resolution:** Clear error messages and automatic UI refresh
4. **Recovery:** Redirect users to updated availability with alternatives

**Time-Based Availability Logic:**
* **Past Slot Detection:** Automatic marking of slots with start time < current time
* **Periodic Updates:** Timer-based refresh for time-sensitive availability
* **Timezone Handling:** Consistent time handling across client and server

## **3\. Iteration Plan & Task List**

### **General Iteration Approach:**

* Each iteration aims to deliver a testable, working piece of functionality.  
* Test-Driven Development (TDD) will be prioritized: Write tests before or alongside feature code.  
* Regular code reviews and refactoring.

### **Iteration 1: Core Setup, User Auth (Email), Viewing Availability (MVP)**

**Goal:** Establish the project foundation, implement basic email authentication, and allow users to see available time slots. No booking functionality yet.

**User Stories for Iteration 1:**

* As a developer, I want to set up a new Flutter project with Supabase integration and Riverpod state management.  
* As a user, I want to be able to sign up for a new account using my email and a password.  
* As a user, I want to be able to log in to my account using my email and password.  
* As a user, I want to be able to log out of my account.  
* As a user, I want to see a calendar or list view of days.  
* As a user, when I select a day, I want to see the available 90-minute time slots for the pitch based on operating hours (05:30 \- 22:30).  
* As an admin (developer initially via Supabase console), I want to define the pitch's operating hours and default slot duration.

**Technical Tasks for Iteration 1:**

1. **Project Setup:**  
   * \[ \] Create a new Flutter project.  
   * \[ \] Initialize Git repository.  
   * \[ \] Set up Supabase project.  
   * \[ \] Configure Flutter app with Supabase credentials.  
   * \[ \] Add supabase\_flutter, flutter\_riverpod, freezed\_annotation, json\_annotation, go\_router, intl packages.  
   * \[ \] Add freezed, json\_serializable, build\_runner as dev dependencies. (Consider riverpod\_annotation & riverpod\_generator if using Riverpod's code generation).  
   * \[ \] Basic project structure (folders for screens, widgets, services, models, providers).  
   * \[ \] Initial freezed model class setup (e.g., for User Profile, PitchSettings).  
   * \[ \] Setup ProviderScope at the root of the application for Riverpod.  
   * \[ \] Configure go\_router for basic navigation.  
2. **Supabase Schema (Iteration 1):**  
   * \[ \] Define users table (Supabase Auth handles this, but profiles might be needed).  
     * profiles table: id (UUID, FK to auth.users), full\_name (TEXT), avatar\_url (TEXT, nullable), created\_at (TIMESTAMPTZ), updated\_at (TIMESTAMPTZ).  
   * \[ \] Define pitch\_settings table (or a way to store global settings):  
     * id (SERIAL PK) or single row.  
     * open\_time (TIME) \- e.g., '05:30:00'  
     * close\_time (TIME) \- e.g., '22:30:00'  
     * slot\_duration\_minutes (INTEGER) \- e.g., 90  
     * cancellation\_window\_hours (INTEGER) \- e.g., 4  
   * \[ \] Define bookings table (basic for now, to check against for availability):  
     * id (SERIAL PK)  
     * user\_id (UUID, FK to auth.users)  
     * slot\_start\_time (TIMESTAMPTZ)  
     * slot\_end\_time (TIMESTAMPTZ)  
     * status (TEXT) \- e.g., 'confirmed', 'cancelled' (default 'confirmed')  
     * created\_at (TIMESTAMPTZ)  
   * \[ \] Set up initial RLS policies for these tables (e.g., users can read pitch\_settings, users can only read their own future bookings for now).  
3. **Authentication (Email/Password) with Riverpod:**  
   * \[ \] Create AuthRepository/Service class to handle Supabase auth calls.  
   * \[ \] Create Riverpod providers for auth state (e.g., authRepositoryProvider, authStateChangesProvider, currentUserProvider).  
   * \[ \] Create SignUp screen UI (email, password, confirm password fields).  
   * \[ \] Implement SignUp logic using Riverpod providers and AuthRepository. (Write tests)  
   * \[ \] Create Login screen UI (email, password fields).  
   * \[ \] Implement Login logic using Riverpod providers and AuthRepository. (Write tests)  
   * \[ \] Implement Logout functionality using Riverpod providers. (Write tests)  
   * \[ \] Use authStateChangesProvider with go\_router to handle redirects (e.g., to home/login based on auth status).  
   * \[ \] Basic error handling and user feedback for auth, managed via Riverpod state.  
4. **Viewing Availability with Riverpod:**  
   * \[ \] Create AvailabilityRepository/Service class.  
   * \[ \] Create Riverpod providers for fetching pitch\_settings and bookings.  
   * \[ \] Create a screen to display pitch availability (e.g., "AvailabilityScreen").  
   * \[ \] UI to select a date (e.g., using a calendar widget or simple date picker).  
   * \[ \] Logic (potentially in a Riverpod provider/notifier) to:  
     * Fetch pitch\_settings.  
     * Generate potential time slots for a selected day.  
     * Fetch existing bookings for the selected day.  
     * Determine available slots.  
       (Write tests for this logic)  
   * \[ \] Display available slots in a list or grid, reacting to state changes from Riverpod.  
   * \[ \] UI should be responsive for mobile and web.  
5. **Testing (TDD Approach):**  
   * \[ \] Set up Flutter testing environment, including testing Riverpod providers.  
   * \[ \] Write unit tests for business logic (slot generation, availability calculation, auth service methods, model transformations, repository methods).  
   * \[ \] Write widget tests for UI components (SignUp/Login forms, Availability display), providing mock Riverpod providers.  
6. **Basic Admin Setup (Manual via Supabase Console for Iteration 1):**  
   * \[ \] Manually populate pitch\_settings in Supabase.  
   * \[ \] Manually add a few test bookings to verify availability logic.

### **Iteration 2: Booking Creation, Real-Time Updates & Race Condition Handling**

**Goal:** Allow users to book an available slot, view their own bookings, implement real-time availability updates, and prevent race conditions/double bookings.

**User Stories for Iteration 2:**

**Core Booking Functionality:**
* As a user, when I see an available slot, I want to be able to select it and confirm my booking.  
* As a user, I want to receive a simple in-app confirmation after booking.  
* As a user, I want to have a dedicated section in the app where I can see my upcoming bookings.

**Real-Time Availability Updates:**
* As a user, after I successfully book a slot, I want to immediately see the availability screen refresh to show that slot as unavailable.  
* As a user, when I'm viewing the availability screen, I want to see slots automatically update to unavailable when other users book them (within 2-3 seconds).  
* As a user, when viewing past time slots (where start time has passed), I want them to appear as "Past" or "Unavailable" and not bookable.  
* As a user, when slots become available due to cancellations, I want to see them appear as bookable in real-time.

**Race Condition & Conflict Handling:**
* As a user, when I attempt to book a slot that was just booked by another user, I want to receive a clear error message: "This slot was just booked by another user. Please select a different time."  
* As a user, after receiving a booking conflict error, I want to see the availability screen automatically refresh with the most current slot statuses.  
* As a system, I want to ensure that only one user can successfully book any specific time slot, even if multiple users click "Book" simultaneously.  
* As a user, if I'm on the booking confirmation screen and the slot becomes unavailable, I want to be redirected back to availability with a clear explanation.

**Performance & UX:**
* As a user, when I click "Book," I want to see immediate visual feedback (loading state) while the booking is being processed.  
* As a user, I want booking operations to complete within 3 seconds under normal conditions.  
* As a user, if a booking operation takes longer than expected, I want to see a progress indicator and not be able to accidentally submit multiple booking requests.

**Technical Tasks for Iteration 2 (High-Level):**

* \[ \] UI for booking confirmation.  
* \[ \] Extend BookingRepository/Service and Riverpod providers for creating bookings. (Write tests)  
  * Ensure atomicity/checks to prevent double bookings (possibly via Supabase functions or careful client-side logic).  
* \[ \] "My Bookings" screen UI.  
* \[ \] Riverpod providers to fetch and display the current user's upcoming bookings. (Write tests)  
* \[ \] Update RLS for bookings table (users can create bookings, can read their own).  
* \[ \] Basic email notification for booking confirmation (using Supabase Edge Function with a transactional email provider like SendGrid/Resend \- setup might be part of this or a dedicated task).

### Pre-Iteration 3: Enhanced Booking Flow with Confirmation

*   **Modify `AvailabilityScreen`**:
    *   Instead of direct booking, tapping "Book" on a time slot will navigate to a new `BookingConfirmationScreen`.
    *   Slot details (pitch, time) will be passed to the confirmation screen.
*   **Create `BookingConfirmationScreen` (New Widget)**:
    *   Display a summary of the booking (pitch, date, time, "Football included").
    *   **Actions**:
        *   **"Confirm & Proceed to Book" Button**:
            *   Invokes the existing `createBooking` logic.
            *   Handles loading, success (navigate to My Bookings/Availability, refresh data), and error states.
        *   **"Cancel" Button**: Navigates back to `AvailabilityScreen`.
*   **Future Payment Integration**:
    *   This `BookingConfirmationScreen` will be the place to integrate the "Proceed to Payment" step in Iteration 5. Bookings might initially have a 'pending_payment' status.

### **Iteration 3: Cancellation & Basic Add-ons (No Payment)**

**Goal:** Implement booking cancellation and allow selection of pre-defined packages/items (without payment processing).

**User Stories for Iteration 3:**

* As a user, I want to be able to cancel an upcoming booking from the "My Bookings" screen, provided it's outside the 4-hour cancellation window.  
* As a user, I want to see the defined packages (Match, Training) or individual items when booking.  
* As a user, I want to select these add-ons, and they should be associated with my booking.

**Technical Tasks for Iteration 3 (High-Level):**

* \[ \] UI for cancellation confirmation.  
* \[ \] Extend BookingRepository/Service and Riverpod providers for cancelling bookings if within policy. (Write tests)  
* \[ \] Supabase schema for additional\_items (name, description, price \- though price not used for payment yet) and booking\_items (linking bookings to items).  
* \[ \] UI to display and select add-ons during booking flow.  
* \[ \] Logic (managed by Riverpod) to save selected add-ons with the booking. (Write tests)  
* \[ \] Basic email notification for cancellation.

### **Iteration 4: Admin Panel MVP (View Bookings, Block Slots)**

**Goal:** Provide a basic web-based admin interface for the pitch owner.

**User Stories for Iteration 4:**

* As an admin, I want to log in with a separate admin account.  
* As an admin, I want to view a list/calendar of all bookings.  
* As an admin, I want to manually mark certain time slots as unavailable (e.g., for maintenance).

**Technical Tasks for Iteration 4 (High-Level):**

* \[ \] Admin login mechanism (could be a separate Flutter web app or a distinct section in the main app with role-based access, managed by Riverpod and GoRouter).  
* \[ \] Supabase: Differentiate admin users (e.g., custom claim or separate table). Update RLS.  
* \[ \] Admin UI to view all bookings (table/calendar), with data fetched via Riverpod providers.  
* \[ \] Admin UI to create "blocker" entries (could be special bookings or a separate blocked\_slots table), actions managed by Riverpod.  
* \[ \] Logic for availability screen to respect these admin-blocked slots.

### **Iteration 5: Payment Gateway Integration (Complex)**

**Goal:** Integrate local payment gateways. This is a significant iteration and may need to be broken down further.

**User Stories for Iteration 5:**

* As a user, I want to pay for my booking and add-ons using Airtel Money.  
* As a user, I want to pay for my booking and add-ons using TNM Mpamba.  
* As a user, I want to pay using local bank options.

**Technical Tasks for Iteration 5 (High-Level):**

* \[ \] Research and select appropriate Flutter packages or SDKs for Airtel Money, TNM Mpamba, and bank payments. This is critical. Direct API integration might be needed via Supabase Edge Functions if no Flutter SDKs exist.  
* \[ \] UI for payment method selection and information input.  
* \[ \] Securely handle payment processing flow (likely involving Supabase Edge Functions to communicate with payment provider APIs, with state managed by Riverpod).  
* \[ \] Update booking status to 'confirmed\_paid' upon successful payment.  
* \[ \] Handle payment failures and pending states.  
* \[ \] Store transaction references.

### **Further Iterations (Beyond MVP):**

* SMS Authentication & Notifications.  
* Advanced Admin Features (Pricing Management, User Management, Reporting).  
* User Profile Management (update details, password reset).  
* Push Notifications for reminders/confirmations.  
* UI Polish & Animations.  
* Enhanced error handling and logging.  
* User Reviews/Ratings for the pitch.  
* Promotions/Discount Codes.

## **3.1. Real-Time Updates & Race Condition Implementation Strategy** 

### **3.1.1. Booking Flow with Race Condition Prevention**

**Optimistic Booking Flow:**
```
1. User clicks "Book" on available slot
2. UI immediately shows loading state  
3. Client sends booking request to Supabase Edge Function
4. Edge Function performs atomic booking creation:
   - Validates slot is still available
   - Creates booking record in transaction
   - Returns success/failure with specific error codes
5. Client handles response:
   - Success: Show success dialog, refresh availability
   - Conflict: Show error message, refresh availability
   - Other error: Show generic error, allow retry
```

**Multi-User Conflict Scenarios:**
- **Scenario A:** User A and User B both see slot available at 16:00-17:30
- **Scenario B:** User A clicks "Book" at T=0, User B clicks "Book" at T=0.5 seconds  
- **Resolution:** Database unique constraint ensures only first successful transaction wins
- **User Experience:** Second user gets clear "slot just booked" message with updated availability

### **3.1.2. Real-Time Data Synchronization**

**Supabase Realtime Integration:**
```dart
// Example Riverpod provider for real-time availability
@riverpod
Stream<List<TimeSlotInfo>> realTimeAvailableSlots(
  RealTimeAvailableSlotsRef ref,
  DateTime selectedDate,
  int pitchId,
) {
  final supabase = ref.watch(supabaseClientProvider);
  
  // Combine initial data with real-time updates
  return supabase
    .from('bookings')
    .stream(primaryKey: ['id'])
    .eq('pitch_id', pitchId)
    .gte('slot_start_time', selectedDate.startOfDay)
    .lt('slot_start_time', selectedDate.endOfDay)
    .map((bookings) => _calculateAvailableSlots(bookings, selectedDate));
}
```

**Update Triggers:**
- **New Booking:** Real-time event → Update availability → Refresh UI
- **Cancellation:** Real-time event → Update availability → Refresh UI  
- **Time Passage:** Timer-based update → Mark past slots unavailable
- **App Resume:** Force refresh to get latest state

### **3.1.3. Error Handling & User Feedback**

**Error Types & Messages:**
```dart
enum BookingError {
  slotUnavailable("This slot was just booked by another user"),
  slotInPast("Cannot book slots in the past"), 
  networkError("Connection issue - please try again"),
  serverError("Booking failed - please contact support"),
  userLimitReached("You have reached your booking limit");
}
```

**Recovery Actions:**
- **Slot Unavailable:** Auto-refresh availability, suggest similar slots
- **Network Error:** Retry button with exponential backoff
- **Server Error:** Contact support option, error ID for debugging

### **3.1.4. Performance Considerations**

**Optimization Strategies:**
- **Targeted Subscriptions:** Only subscribe to relevant date ranges
- **Debounced Updates:** Batch rapid real-time updates to prevent UI flicker
- **Efficient Queries:** Indexed database queries for fast availability calculation
- **Client-Side Caching:** Cache recent availability data with smart invalidation
- **Connection Management:** Graceful handling of connection drops and reconnection

## **4\. Supabase Schema Design**

### **4.1. Core Tables**

**profiles** (extends auth.users)
```sql
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**pitch_settings**
```sql
CREATE TABLE pitch_settings (
  id SERIAL PRIMARY KEY,
  open_time TIME NOT NULL DEFAULT '05:30:00',
  close_time TIME NOT NULL DEFAULT '22:30:00', 
  slot_duration_minutes INTEGER NOT NULL DEFAULT 90,
  cancellation_window_hours INTEGER NOT NULL DEFAULT 4,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**bookings** (Enhanced for race condition prevention)
```sql
CREATE TABLE bookings (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  pitch_id INTEGER NOT NULL DEFAULT 1, -- Future-proofing for multiple pitches
  slot_start_time TIMESTAMPTZ NOT NULL,
  slot_end_time TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL DEFAULT 'confirmed' CHECK (status IN ('confirmed', 'cancelled', 'pending_payment')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  version INTEGER DEFAULT 1, -- For optimistic locking (optional)
  
  -- Prevent overlapping bookings for the same pitch
  CONSTRAINT no_overlapping_bookings UNIQUE (pitch_id, slot_start_time, slot_end_time),
  
  -- Ensure valid time ranges
  CONSTRAINT valid_time_range CHECK (slot_end_time > slot_start_time),
  
  -- Prevent booking in the past (server-side enforcement)
  CONSTRAINT no_past_bookings CHECK (slot_start_time > NOW() - INTERVAL '5 minutes')
);

-- Index for efficient availability queries
CREATE INDEX idx_bookings_time_range ON bookings (pitch_id, slot_start_time, slot_end_time) 
WHERE status != 'cancelled';

-- Index for user's bookings
CREATE INDEX idx_bookings_user_time ON bookings (user_id, slot_start_time) 
WHERE status != 'cancelled';
```

### **4.2. Race Condition Prevention Constraints**

**Unique Booking Constraint:**
```sql
-- This constraint prevents double bookings at the database level
-- Even if two users try to book the same slot simultaneously, only one will succeed
CREATE UNIQUE INDEX unique_active_booking_slots 
ON bookings (pitch_id, slot_start_time, slot_end_time) 
WHERE status IN ('confirmed', 'pending_payment');
```

**Booking Validation Function:**
```sql
CREATE OR REPLACE FUNCTION validate_booking_slot(
  p_pitch_id INTEGER,
  p_start_time TIMESTAMPTZ, 
  p_end_time TIMESTAMPTZ
) RETURNS BOOLEAN AS $$
DECLARE
  existing_booking_count INTEGER;
BEGIN
  -- Check for any overlapping active bookings
  SELECT COUNT(*) INTO existing_booking_count
  FROM bookings 
  WHERE pitch_id = p_pitch_id 
    AND status IN ('confirmed', 'pending_payment')
    AND (
      (slot_start_time <= p_start_time AND slot_end_time > p_start_time) OR
      (slot_start_time < p_end_time AND slot_end_time >= p_end_time) OR  
      (slot_start_time >= p_start_time AND slot_end_time <= p_end_time)
    );
    
  RETURN existing_booking_count = 0;
END;
$$ LANGUAGE plpgsql;
```

### **4.3. Row Level Security (RLS) Policies**

```sql
-- Enable RLS
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE pitch_settings ENABLE ROW LEVEL SECURITY;

-- Bookings policies
CREATE POLICY "Users can view their own bookings" ON bookings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own bookings" ON bookings  
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own bookings" ON bookings
  FOR UPDATE USING (auth.uid() = user_id);

-- Pitch settings policies (readable by all authenticated users)
CREATE POLICY "Authenticated users can view pitch settings" ON pitch_settings
  FOR SELECT USING (auth.role() = 'authenticated');

-- Profiles policies  
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);
```

### **4.4. Supabase Edge Functions**

**Atomic Booking Creation Function:**
```typescript
// Supabase Edge Function: create_booking_atomic
export default async function handler(req: Request) {
  const { pitch_id, slot_start_time, slot_end_time, user_id } = await req.json();
  
  // Start transaction
  const { data, error } = await supabase.rpc('create_booking_atomic', {
    p_pitch_id: pitch_id,
    p_start_time: slot_start_time,
    p_end_time: slot_end_time,
    p_user_id: user_id
  });
  
  if (error) {
    if (error.code === '23505') { // Unique constraint violation
      return new Response(
        JSON.stringify({ error: 'SLOT_UNAVAILABLE', message: 'This slot was just booked by another user' }),
        { status: 409 }
      );
    }
    return new Response(JSON.stringify({ error: 'BOOKING_FAILED', message: error.message }), { status: 500 });
  }
  
  return new Response(JSON.stringify({ success: true, booking_id: data }), { status: 200 });
}
```

### **4.5. Future Tables (Iteration 3+)**

* **additional_items** (for add-ons/packages)
* **booking_items** (linking bookings to items)  
* **blocked_slots** (for admin-blocked time slots)
* **booking_history** (for audit trail)

### **4.6. Database Triggers for Real-Time Updates**

```sql
-- Trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_bookings_updated_at
  BEFORE UPDATE ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
```

## **5\. UI/UX Style Guide (Basic \- To be expanded)**

* **Primary Color:** (To be decided \- perhaps a vibrant green or blue associated with sports?)  
* **Accent Color:** (To be decided)  
* **Typography:**  
  * Font Family: (Choose a clean, readable sans-serif like Roboto, Open Sans \- available via google\_fonts package)  
  * Headings, Body, Captions.  
* **Layout:** Clean, spacious, mobile-first, responsive for web.

## **6\. Testing Strategy**

* **Unit Tests:** For individual functions, methods, and classes (especially business logic in services/models, repository methods). Located in test/unit\_tests.  
* **Widget Tests:** For individual Flutter widgets, ensuring they render correctly and respond to interactions. Mock Riverpod providers using ProviderContainer or overrides. Located in test/widget\_tests.  
* **Integration Tests (Flutter Driver /** integration\_test **package):** For testing complete flows or screen interactions, including interaction with Supabase and Riverpod providers. Located in integration\_test.  
* Aim for high test coverage, especially for critical paths like auth, booking, and availability logic.

### **6.1. Race Condition & Real-Time Testing Strategy**

**Concurrent Booking Tests:**
```dart
// Example test structure for race condition testing
testWidgets('prevents double booking when multiple users book simultaneously', (tester) async {
  // Setup: Two users see the same available slot
  // Action: Both attempt to book the same slot at nearly same time
  // Assert: Only one booking succeeds, other gets conflict error
  // Verify: Availability updates correctly for both users
});
```

**Database Constraint Tests:**
```sql
-- SQL tests for database-level race condition prevention
DO $$ 
BEGIN
  -- Test: Simultaneous booking attempts should fail with unique constraint
  -- Setup concurrent transactions
  -- Verify only one succeeds
END $$;
```

**Real-Time Synchronization Tests:**
```dart
// Test real-time updates propagate correctly
testWidgets('availability updates when other user books slot', (tester) async {
  // Setup: User A viewing availability screen
  // Action: Simulate User B booking a slot (via direct DB insert)
  // Assert: User A's screen updates within 3 seconds
  // Verify: Correct slot shows as unavailable
});
```

**Performance Tests:**
- **Load Testing:** 50+ concurrent users booking different slots
- **Stress Testing:** Multiple users attempting same slot simultaneously  
- **Connection Testing:** Real-time updates during poor network conditions
- **Memory Testing:** Long-running real-time subscriptions

**Edge Case Tests:**
- **Time Boundary:** Booking slots that become past while user is on booking screen
- **Network Recovery:** Real-time reconnection after connection loss
- **Stale Data:** User with old cached data attempting to book
- **Rapid Updates:** Multiple booking/cancellation events in quick succession

## **7\. Useful Links & Resources**

* **Flutter:**  
  * Official Documentation: [https://docs.flutter.dev/](https://docs.flutter.dev/)  
  * Cookbook: [https://docs.flutter.dev/cookbook](https://docs.flutter.dev/cookbook)  
  * Widget Catalog: [https://docs.flutter.dev/development/ui/widgets](https://docs.flutter.dev/development/ui/widgets)  
  * Testing Flutter Apps: [https://docs.flutter.dev/testing](https://docs.flutter.dev/testing)  
  * google\_fonts package: [https://pub.dev/packages/google\_fonts](https://pub.dev/packages/google_fonts)  
* **Dart:**  
  * Official Documentation: [https://dart.dev/guides](https://dart.dev/guides)  
  * Language Tour: [https://dart.dev/guides/language/language-tour](https://dart.dev/guides/language/language-tour)  
* **Supabase:**  
  * Official Documentation: [https://supabase.com/docs](https://supabase.com/docs)  
  * Supabase Flutter Guide: [https://supabase.com/docs/guides/getting-started/tutorials/with-flutter](https://supabase.com/docs/guides/getting-started/tutorials/with-flutter)  
  * supabase\_flutter package: [https://pub.dev/packages/supabase\_flutter](https://pub.dev/packages/supabase_flutter)  
  * Row Level Security (RLS): [https://supabase.com/docs/guides/auth/row-level-security](https://supabase.com/docs/guides/auth/row-level-security)  
  * Edge Functions: [https://supabase.com/docs/guides/functions](https://supabase.com/docs/guides/functions)  
* **Model Generation & Immutability:**  
  * freezed: [https://pub.dev/packages/freezed](https://pub.dev/packages/freezed)  
  * json\_serializable: [https://pub.dev/packages/json\_serializable](https://pub.dev/packages/json_serializable)  
  * build\_runner: [https://pub.dev/packages/build\_runner](https://pub.dev/packages/build_runner)  
* **State Management:**  
  * **Riverpod (Chosen):**  
    * Official Documentation: [https://riverpod.dev/](https://riverpod.dev/)  
    * flutter\_riverpod package: [https://pub.dev/packages/flutter\_riverpod](https://pub.dev/packages/flutter_riverpod)  
    * riverpod\_annotation (optional): [https://pub.dev/packages/riverpod\_annotation](https://pub.dev/packages/riverpod_annotation)  
* **Routing:**  
  * go\_router: [https://pub.dev/packages/go\_router](https://pub.dev/packages/go_router)  
* **Payment Gateway Research (Malawi \- To be investigated):**  
  * Airtel Money API (Need to find developer resources)  
  * TNM Mpamba API (Need to find developer resources)  
  * Local Bank APIs (e.g., Standard Bank, NBS Bank \- check their developer portals)  
  * Third-party aggregators if available.

## **Next Steps:**

1. **Review & Refine Iteration 1:** Go over the tasks and user stories for Iteration 1, now incorporating Riverpod.  
2. **Decide on Routing:** Confirm go\_router (recommended with Riverpod for robust navigation and deep linking).  
3. **Begin Development of Iteration 1:** Start with project setup, Supabase schema, initial freezed models, and Riverpod providers.  
4. **Flesh out UI/UX Style Guide:** Start picking colors and fonts.


## Flutter Guide: Availability Screen (Iteration 1)

```Dart

// ignore_for_file: prefer_const_constructors, prefer_const_literals_to_create_immutables
// The above ignores are just for this example to be self-contained.
// In a real project, organize imports and follow linting rules.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart'; // For date formatting

// --- Part 1: Models (using Freezed) ---
// These would typically be in their own files e.g., 'pitch_settings.dart'
// Make sure to run `flutter pub run build_runner build --delete-conflicting-outputs`

part 'flutter_availability_screen_guide_v1.freezed.dart';
part 'flutter_availability_screen_guide_v1.g.dart';

@freezed
class PitchSettings with _$PitchSettings {
  const factory PitchSettings({
    // Using String for time for simplicity, parse to TimeOfDay in logic
    @JsonKey(name: 'open_time') required String openTime, // "HH:MM:SS"
    @JsonKey(name: 'close_time') required String closeTime, // "HH:MM:SS"
    @JsonKey(name: 'slot_duration_minutes') required int slotDurationMinutes,
    // id and cancellation_window_hours from original plan can be added if needed now
  }) = _PitchSettings;

  factory PitchSettings.fromJson(Map<String, dynamic> json) =>
      _$PitchSettingsFromJson(json);
}

// Mock Booking Data Structure (for this guide)
@freezed
class MockBooking with _$MockBooking {
  const factory MockBooking({
    required DateTime startTime,
    required DateTime endTime,
  }) = _MockBooking;
}

// --- Part 2: Riverpod Providers & Repositories ---
// These would be in separate files in your data/application layers.

// Mock Supabase Client (Replace with actual supabaseClientProvider from your project)
final mockSupabaseClientProvider = Provider<dynamic>((ref) {
  // In a real app, this is your SupabaseClient instance
  return null; 
});

// Repository for Pitch Settings
final pitchSettingsRepositoryProvider = Provider<PitchSettingsRepository>((ref) {
  // final client = ref.watch(mockSupabaseClientProvider); // Use actual client
  return PitchSettingsRepository(/*client*/);
});

class PitchSettingsRepository {
  // final SupabaseClient _client; // Uncomment when using real Supabase
  // PitchSettingsRepository(this._client); // Uncomment

  PitchSettingsRepository(); // For mock

  Future<PitchSettings> getPitchSettings() async {
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 500));
    // Mock data - In real app, fetch from Supabase:
    // final response = await _client.from('pitch_settings').select().single().execute();
    // return PitchSettings.fromJson(response.data);
    return PitchSettings(
      openTime: "05:30:00",
      closeTime: "22:30:00",
      slotDurationMinutes: 90,
    );
  }
}

// Provider to fetch pitch settings
final pitchSettingsProvider = FutureProvider<PitchSettings>((ref) async {
  final repository = ref.watch(pitchSettingsRepositoryProvider);
  return repository.getPitchSettings();
});

// Provider for selected date (managed by StateProvider for mutability from UI)
final selectedDateProvider = StateProvider<DateTime>((ref) => DateTime.now());

// Mock Booked Slots Provider (for demonstration)
// In a real app, this would fetch from 'bookings' table based on selectedDateProvider
final mockBookedSlotsProvider = Provider.family<List<MockBooking>, DateTime>((ref, date) {
  // Filter MOCK_BOOKED_SLOTS_DATA by date
  List<MockBooking> slotsForDate = [];
  if (DateFormat('yyyy-MM-dd').format(date) == '2025-05-22') {
    slotsForDate.add(MockBooking(startTime: DateTime(2025,5,22,10,0), endTime: DateTime(2025,5,22,11,30)));
    slotsForDate.add(MockBooking(startTime: DateTime(2025,5,22,14,30), endTime: DateTime(2025,5,22,16,0)));
  }
  if (DateFormat('yyyy-MM-dd').format(date) == '2025-05-23') {
     slotsForDate.add(MockBooking(startTime: DateTime(2025,5,23,8,0), endTime: DateTime(2025,5,23,9,30)));
  }
  return slotsForDate;
});


// Provider for generating available time slots
final availableTimeSlotsProvider = Provider<List<TimeSlot>>((ref) {
  final pitchSettingsData = ref.watch(pitchSettingsProvider);
  final selectedDate = ref.watch(selectedDateProvider);
  final bookedSlots = ref.watch(mockBookedSlotsProvider(selectedDate));

  return pitchSettingsData.when(
    data: (settings) {
      List<TimeSlot> slots = [];
      
      TimeOfDay openTime = TimeOfDay(
        hour: int.parse(settings.openTime.split(':')[0]),
        minute: int.parse(settings.openTime.split(':')[1]),
      );
      TimeOfDay closeTime = TimeOfDay(
        hour: int.parse(settings.closeTime.split(':')[0]),
        minute: int.parse(settings.closeTime.split(':')[1]),
      );

      DateTime currentSlotStart = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        openTime.hour,
        openTime.minute,
      );

      DateTime pitchCloseDateTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        closeTime.hour,
        closeTime.minute,
      );

      while (true) {
        DateTime currentSlotEnd = currentSlotStart.add(Duration(minutes: settings.slotDurationMinutes));
        
        if (currentSlotEnd.isAfter(pitchCloseDateTime)) {
          break; 
        }

        bool isBooked = bookedSlots.any((booked) =>
            (currentSlotStart.isAtSameMomentAs(booked.startTime) || currentSlotStart.isAfter(booked.startTime)) &&
            currentSlotStart.isBefore(booked.endTime));

        slots.add(TimeSlot(
          startTime: currentSlotStart,
          endTime: currentSlotEnd,
          isBooked: isBooked,
        ));
        
        currentSlotStart = currentSlotEnd;
      }
      return slots;
    },
    loading: () => [],
    error: (err, stack) => [],
  );
});

// Helper class for TimeSlot UI
class TimeSlot {
  final DateTime startTime;
  final DateTime endTime;
  final bool isBooked;

  TimeSlot({required this.startTime, required this.endTime, required this.isBooked});
}


// --- Part 3: Flutter UI (AvailabilityScreen Widget) ---
// This would be in 'availability_screen.dart' in your presentation layer.

class AvailabilityScreen extends ConsumerWidget {
  const AvailabilityScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pitchSettingsAsync = ref.watch(pitchSettingsProvider);
    final selectedDate = ref.watch(selectedDateProvider);
    final timeSlots = ref.watch(availableTimeSlotsProvider);

    final DateFormat displayDateFormat = DateFormat('EEEE, MMMM d, yyyy');
    final DateFormat slotTimeFormat = DateFormat('HH:mm');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Pitch Availability'),
        backgroundColor: Colors.sky[500], // Softer blue
        actions: [
          IconButton(
            icon: Icon(Icons.person_outline),
            onPressed: () {
              // Placeholder for user profile or logout
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Invalidate providers to refetch data
          ref.invalidate(pitchSettingsProvider);
          // If booked slots were fetched via a FutureProvider, invalidate it too.
          // For this example, mockBookedSlotsProvider is a simple Provider.family,
          // it will re-evaluate if selectedDate changes.
        },
        child: ListView( // Use ListView for scrollability if content overflows
          padding: const EdgeInsets.all(16.0),
          children: [
            // Date Selection Section
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Select Date',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        IconButton(
                          icon: Icon(Icons.calendar_today, color: Colors.sky[600]),
                          onPressed: () async {
                            final DateTime? picked = await showDatePicker(
                              context: context,
                              initialDate: selectedDate,
                              firstDate: DateTime.now().subtract(Duration(days: 0)), // Can't select past
                              lastDate: DateTime.now().add(Duration(days: 365)),
                            );
                            if (picked != null && picked != selectedDate) {
                              ref.read(selectedDateProvider.notifier).state = picked;
                            }
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                    // Horizontal Date Scroller (Simplified for this guide)
                    SizedBox(
                      height: 70,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: 7, // Show 7 days for example
                        itemBuilder: (context, index) {
                          final date = DateTime.now().add(Duration(days: index));
                          final bool isSelected = DateUtils.isSameDay(date, selectedDate);
                          return GestureDetector(
                            onTap: () {
                              ref.read(selectedDateProvider.notifier).state = date;
                            },
                            child: Container(
                              width: 60,
                              margin: EdgeInsets.symmetric(horizontal: 4),
                              decoration: BoxDecoration(
                                color: isSelected ? Colors.sky[500] : Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                                border: isSelected ? Border.all(color: Colors.sky[700]!, width: 2) : null,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    DateFormat('E').format(date), // Day of week (e.g., Mon)
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: isSelected ? Colors.white : Colors.black87,
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    DateFormat('d').format(date), // Day number
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: isSelected ? Colors.white : Colors.black,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    SizedBox(height: 12),
                    Center(
                      child: Text(
                        'Selected: ${displayDateFormat.format(selectedDate)}',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.grey[700]),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 20),

            // Time Slots Section
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                     Text(
                      'Available Slots',
                       style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                    ),
                    SizedBox(height: 12),
                    pitchSettingsAsync.when(
                      data: (settings) {
                        if (timeSlots.isEmpty) {
                          return Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 32.0),
                              child: Column(
                                children: [
                                  Icon(Icons.event_busy_outlined, size: 48, color: Colors.grey[400]),
                                  SizedBox(height: 8),
                                  Text('No slots available for this date.', style: TextStyle(color: Colors.grey[600])),
                                ],
                              ),
                            ),
                          );
                        }
                        return GridView.builder(
                          shrinkWrap: true, // Important for GridView inside ListView
                          physics: NeverScrollableScrollPhysics(), // Disable GridView's own scrolling
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2, // Responsive columns
                            crossAxisSpacing: 10,
                            mainAxisSpacing: 10,
                            childAspectRatio: 2.5, // Adjust for desired button shape
                          ),
                          itemCount: timeSlots.length,
                          itemBuilder: (context, index) {
                            final slot = timeSlots[index];
                            return ElevatedButton(
                              onPressed: slot.isBooked ? null : () {
                                // TODO: Navigate to booking confirmation screen (Iteration 2)
                                print('Selected slot: ${slotTimeFormat.format(slot.startTime)} - ${slotTimeFormat.format(slot.endTime)}');
                                // Example: context.go('/book_slot', extra: slot);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: slot.isBooked ? Colors.red[200] : Colors.sky[100],
                                foregroundColor: slot.isBooked ? Colors.red[700] : Colors.sky[700],
                                disabledBackgroundColor: Colors.red[100],
                                disabledForegroundColor: Colors.red[400],
                                padding: EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: slot.isBooked ? 0 : 2,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    '${slotTimeFormat.format(slot.startTime)} - ${slotTimeFormat.format(slot.endTime)}',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    slot.isBooked ? 'Booked' : 'Available',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                      loading: () => Center(child: CircularProgressIndicator()),
                      error: (err, stack) => Center(child: Text('Error loading settings: ${err.toString()}')),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// --- Main App Setup (Simplified for this guide) ---
// In your real app, this would be in main.dart with GoRouter setup
void main() {
  runApp(
    ProviderScope( // Essential for Riverpod
      child: MaterialApp(
        title: 'Pitch Booking Flutter Guide',
        theme: ThemeData(
          primarySwatch: Colors.sky, // Using sky blue as primary
          fontFamily: 'Inter', // Assuming Inter font is set up in pubspec.yaml
          cardTheme: CardTheme(
            elevation: 0, // Default to no elevation for cards, add explicitly if needed
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
          ),
        ),
        home: AvailabilityScreen(), // Directly show the screen for this guide
        // In real app: routerConfig: ref.watch(goRouterProvider),
      ),
    ),
  );
}

// --- Generated Freezed/JsonSerializable code ---
// This would be in flutter_availability_screen_guide_v1.freezed.dart
// and flutter_availability_screen_guide_v1.g.dart
// You need to run the build_runner command to generate these.
// For brevity, I'm not including the generated code here.
// Just ensure your Freezed classes have the necessary annotations.

```