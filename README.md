<div id="readme-top"></div>

<!-- 
PROJECT LOGO 
<br />
<div align="center">
  <a href="https://github.com/your_username/skills">
    <img src="images/logo.png" alt="Logo" width="80" height="80">
  </a>
-->

<h3 align="center">Skills - Football Pitch Booking App</h3>

  <p align="center">
    A Flutter application for booking local football pitches, built with Supabase and Riverpod
    <br />
    <a href="docs/"><strong>📚 Explore the docs »</strong></a>
    <br />
    <br />
    <!-- <a href="https://github.com/your_username/skills">View Demo</a>
    · -->
    <a href="https://github.com/your_username/skills/issues">Report Bug</a>
    ·
    <a href="https://github.com/your_username/skills/issues">Request Feature</a>
  </p>
</div>

<!-- TABLE OF CONTENTS -->
<details>
  <summary>Table of Contents</summary>
  <ol>
    <li><a href="#about-the-project">About The Project</a></li>
    <li><a href="#built-with">Built With</a></li>
    <li><a href="#quick-start">Quick Start</a></li>
    <li><a href="#project-status--documentation">Project Status & Documentation</a></li>
    <li><a href="#contributing">Contributing</a></li>
    <li><a href="#license">License</a></li>
    <li><a href="#contact">Contact</a></li>
  </ol>
</details>

## About The Project

A Flutter application for booking local football pitches with real-time availability, conflict prevention, and dynamic pricing.

**🏆 Current Status**: 95% Complete - Production ready with real-time booking system operational  
**📱 Platform**: Cross-platform (iOS, Android, Web)  
**🏗️ Architecture**: Clean Architecture + Riverpod + Supabase + Edge Functions

<p align="right">(<a href="#readme-top">back to top</a>)</p>

### Built With

* [![Flutter][Flutter.dev]][Flutter-url] - Cross-platform UI framework
* [![Supabase][Supabase.com]][Supabase-url] - Backend-as-a-Service with real-time capabilities  
* [![Riverpod][Riverpod.dev]][Riverpod-url] - Reactive state management

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Quick Start

### Prerequisites
* Flutter SDK 3.0.0+
* Supabase account

### Setup
```bash
git clone https://github.com/your_username/skills.git
cd skills
flutter pub get
# Configure Supabase credentials (see docs/development_guidelines.md)
flutter run
```

For detailed setup and development guidelines, see [Development Guidelines](docs/development_guidelines.md).

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## 📊 Project Status & Documentation

| Document | Purpose | Last Updated |
|----------|---------|--------------|
| **[📊 Project Status](docs/PROJECT_STATUS.md)** | Current progress, sprint status, feature completion | June 13, 2025 |
| **[📝 Development Notes](docs/dev_notes/)** | Session logs, implementation details | June 13, 2025 |
| **[🏗️ Architecture (ADRs)](docs/adrs/)** | Technical decisions and rationale | June 13, 2025 |
| **[⚠️ Known Issues](docs/known_issues.md)** | Bug tracking and resolution plans | June 12, 2025 |
| **[📋 Project Plan](docs/Football%20Pitch%20Booking%20App_%20Project%20Plan%20&%20Develo....md)** | Complete requirements and roadmap | June 12, 2025 |

### 🎯 What's Next
**Current Status**: ✅ **Production Ready** - Real-time booking system operational  
**Recent Achievement**: ADR-006 Complete - Race condition prevention and real-time updates deployed  
**Next Phase**: Payment integration and admin interface (Phase 2)

See [Project Status Dashboard](docs/PROJECT_STATUS.md) for detailed progress tracking.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Contributing

This project is in active development. Contributions welcome!

1. Fork the Project
2. Create Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit Changes (`git commit -m 'Add AmazingFeature'`)
4. Push to Branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

See [Development Guidelines](docs/development_guidelines.md) for coding standards and best practices.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## License

Distributed under the MIT License. See `LICENSE.txt` for more information.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Contact

Your Name - [@your_twitter](https://twitter.com/your_username) - <EMAIL>

Project Link: [https://github.com/your_username/skills](https://github.com/your_username/skills)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

<!-- MARKDOWN LINKS & IMAGES -->
<!-- https://www.markdownguide.org/basic-syntax/#reference-style-links -->
[Flutter.dev]: https://img.shields.io/badge/Flutter-02569B?style=for-the-badge&logo=flutter&logoColor=white
[Flutter-url]: https://flutter.dev/
[Dart.dev]: https://img.shields.io/badge/Dart-0175C2?style=for-the-badge&logo=dart&logoColor=white
[Dart-url]: https://dart.dev/
[Supabase.com]: https://img.shields.io/badge/Supabase-3ECF8E?style=for-the-badge&logo=supabase&logoColor=white
[Supabase-url]: https://supabase.com/
[Riverpod.dev]: https://img.shields.io/badge/Riverpod-0175C2?style=for-the-badge&logo=flutter&logoColor=white
[Riverpod-url]: https://riverpod.dev/
