-- ADR-007 Database Schema: Time Slots Management
-- Created: 2025-06-18
-- Purpose: Replace client-side slot calculation with database-managed slots
-- Goal: Fix 3-slot offset bug and create single source of truth

-- =====================================================
-- CORE TABLE: time_slots
-- =====================================================

CREATE TABLE time_slots (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Slot scheduling information
  pitch_id UUID NOT NULL REFERENCES pitches(id) ON DELETE CASCADE,
  slot_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  
  -- Slot status and booking
  is_booked BOOLEAN NOT NULL DEFAULT FALSE,
  is_available BOOLEAN NOT NULL DEFAULT TRUE, -- Manual override capability
  booking_id UUID NULL REFERENCES bookings(id) ON DELETE SET NULL,
  
  -- Pricing information
  price_per_hour DECIMAL(10,2) NOT NULL,
  
  -- Audit fields
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  -- Data integrity constraints
  CONSTRAINT valid_time_range CHECK (end_time > start_time),
  CONSTRAINT valid_date CHECK (slot_date >= CURRENT_DATE - INTERVAL '1 day'), -- Allow today and future
  CONSTRAINT valid_price CHECK (price_per_hour >= 0),
  CONSTRAINT booking_consistency CHECK (
    (is_booked = TRUE AND booking_id IS NOT NULL) OR 
    (is_booked = FALSE AND booking_id IS NULL)
  )
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary query pattern: fetch slots by pitch and date
CREATE INDEX idx_time_slots_pitch_date ON time_slots(pitch_id, slot_date);

-- Query available slots only
CREATE INDEX idx_time_slots_available ON time_slots(pitch_id, slot_date, is_available) 
WHERE is_available = TRUE;

-- Query booked slots for analytics
CREATE INDEX idx_time_slots_booked ON time_slots(pitch_id, slot_date, is_booked) 
WHERE is_booked = TRUE;

-- Fast lookup by booking ID
CREATE INDEX idx_time_slots_booking ON time_slots(booking_id) 
WHERE booking_id IS NOT NULL;

-- Unique constraint to prevent duplicate slots
CREATE UNIQUE INDEX idx_time_slots_unique ON time_slots(pitch_id, slot_date, start_time);

-- Time-based queries for cleanup/maintenance
CREATE INDEX idx_time_slots_date ON time_slots(slot_date);

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS for secure access
ALTER TABLE time_slots ENABLE ROW LEVEL SECURITY;

-- Anyone can view time slots (needed for availability display)
CREATE POLICY "Anyone can view time slots" ON time_slots
  FOR SELECT USING (true);

-- Only authenticated users can modify slots (admin operations)
CREATE POLICY "Authenticated users can manage slots" ON time_slots
  FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- SLOT GENERATION FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION generate_time_slots(
  p_pitch_id UUID,
  p_date DATE,
  p_force_regenerate BOOLEAN DEFAULT FALSE
) 
RETURNS TABLE(
  slots_created INTEGER,
  message TEXT,
  execution_time_ms INTEGER
) 
LANGUAGE plpgsql
AS $$
DECLARE
  pitch_settings RECORD;
  current_time TIME;
  slot_duration INTERVAL;
  slots_count INTEGER := 0;
  start_time_ms BIGINT;
  end_time_ms BIGINT;
  existing_count INTEGER;
BEGIN
  start_time_ms := EXTRACT(EPOCH FROM clock_timestamp()) * 1000;
  
  -- Input validation
  IF p_pitch_id IS NULL THEN
    RETURN QUERY SELECT 0, 'Error: pitch_id cannot be null', 0;
    RETURN;
  END IF;
  
  IF p_date IS NULL THEN
    RETURN QUERY SELECT 0, 'Error: date cannot be null', 0;
    RETURN;
  END IF;
  
  -- Don't generate slots for past dates (except today)
  IF p_date < CURRENT_DATE THEN
    RETURN QUERY SELECT 0, 'Error: Cannot generate slots for past dates', 0;
    RETURN;
  END IF;
  
  -- Get pitch settings with validation
  SELECT opening_time, closing_time, slot_duration_minutes, price_per_hour
  INTO pitch_settings
  FROM pitches 
  WHERE id = p_pitch_id;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT 0, 'Error: Pitch not found', 0;
    RETURN;
  END IF;
  
  -- Validate pitch settings
  IF pitch_settings.opening_time IS NULL OR 
     pitch_settings.closing_time IS NULL OR 
     pitch_settings.slot_duration_minutes IS NULL OR
     pitch_settings.price_per_hour IS NULL THEN
    RETURN QUERY SELECT 0, 'Error: Incomplete pitch settings', 0;
    RETURN;
  END IF;
  
  IF pitch_settings.opening_time >= pitch_settings.closing_time THEN
    RETURN QUERY SELECT 0, 'Error: Invalid opening/closing times', 0;
    RETURN;
  END IF;
  
  IF pitch_settings.slot_duration_minutes <= 0 THEN
    RETURN QUERY SELECT 0, 'Error: Invalid slot duration', 0;
    RETURN;
  END IF;
  
  -- Check if slots already exist
  SELECT COUNT(*) INTO existing_count
  FROM time_slots 
  WHERE pitch_id = p_pitch_id AND slot_date = p_date;
  
  IF existing_count > 0 THEN
    IF NOT p_force_regenerate THEN
      end_time_ms := EXTRACT(EPOCH FROM clock_timestamp()) * 1000;
      RETURN QUERY SELECT 0, 
        format('Slots already exist for this date (%s existing)', existing_count),
        (end_time_ms - start_time_ms)::INTEGER;
      RETURN;
    ELSE
      -- Delete existing slots if force regenerate
      DELETE FROM time_slots 
      WHERE pitch_id = p_pitch_id AND slot_date = p_date;
    END IF;
  END IF;
  
  -- Generate time slots
  slot_duration := make_interval(mins => pitch_settings.slot_duration_minutes);
  current_time := pitch_settings.opening_time;
  
  -- Safety check: prevent infinite loop
  DECLARE
    max_iterations INTEGER := 100; -- Reasonable limit for daily slots
    iteration_count INTEGER := 0;
  BEGIN
    WHILE current_time + slot_duration <= pitch_settings.closing_time LOOP
      iteration_count := iteration_count + 1;
      
      -- Safety exit
      IF iteration_count > max_iterations THEN
        RETURN QUERY SELECT slots_count, 
          format('Warning: Stopped at %s iterations to prevent infinite loop', max_iterations),
          (EXTRACT(EPOCH FROM clock_timestamp()) * 1000 - start_time_ms)::INTEGER;
        RETURN;
      END IF;
      
      -- Insert slot
      INSERT INTO time_slots (
        pitch_id, 
        slot_date, 
        start_time, 
        end_time, 
        price_per_hour,
        is_booked,
        is_available
      ) VALUES (
        p_pitch_id,
        p_date,
        current_time,
        current_time + slot_duration,
        pitch_settings.price_per_hour,
        FALSE, -- Default to not booked
        TRUE   -- Default to available
      );
      
      slots_count := slots_count + 1;
      current_time := current_time + slot_duration;
    END LOOP;
  END;
  
  end_time_ms := EXTRACT(EPOCH FROM clock_timestamp()) * 1000;
  
  RETURN QUERY SELECT 
    slots_count, 
    format('Successfully generated %s slots for %s', slots_count, p_date::TEXT),
    (end_time_ms - start_time_ms)::INTEGER;
END;
$$;

-- =====================================================
-- BOOKING STATUS TRIGGER FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION update_slot_booking_status()
RETURNS TRIGGER 
LANGUAGE plpgsql
AS $$
DECLARE
  affected_rows INTEGER;
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Mark slot as booked when booking is created
    UPDATE time_slots 
    SET 
      is_booked = TRUE, 
      booking_id = NEW.id, 
      updated_at = NOW()
    WHERE pitch_id = NEW.pitch_id 
      AND slot_date = NEW.booking_date
      AND start_time = NEW.slot_start_time::TIME
      AND end_time = NEW.slot_end_time::TIME;
      
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    -- Log warning if no slot was found (but don't fail)
    IF affected_rows = 0 THEN
      RAISE WARNING 'No matching slot found for booking % (pitch: %, date: %, time: % - %)', 
        NEW.id, NEW.pitch_id, NEW.booking_date, 
        NEW.slot_start_time::TIME, NEW.slot_end_time::TIME;
    END IF;
    
    RETURN NEW;
    
  ELSIF TG_OP = 'DELETE' THEN
    -- Mark slot as available when booking is deleted
    UPDATE time_slots 
    SET 
      is_booked = FALSE, 
      booking_id = NULL, 
      updated_at = NOW()
    WHERE booking_id = OLD.id;
    
    RETURN OLD;
    
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle booking updates (rare, but possible)
    -- First, free the old slot if booking details changed
    IF OLD.pitch_id != NEW.pitch_id OR 
       OLD.booking_date != NEW.booking_date OR
       OLD.slot_start_time != NEW.slot_start_time OR
       OLD.slot_end_time != NEW.slot_end_time THEN
       
      UPDATE time_slots 
      SET 
        is_booked = FALSE, 
        booking_id = NULL, 
        updated_at = NOW()
      WHERE booking_id = OLD.id;
      
      -- Then, book the new slot
      UPDATE time_slots 
      SET 
        is_booked = TRUE, 
        booking_id = NEW.id, 
        updated_at = NOW()
      WHERE pitch_id = NEW.pitch_id 
        AND slot_date = NEW.booking_date
        AND start_time = NEW.slot_start_time::TIME
        AND end_time = NEW.slot_end_time::TIME;
    END IF;
    
    RETURN NEW;
  END IF;
  
  RETURN NULL;
END;
$$;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Trigger to automatically update slot status when bookings change
CREATE TRIGGER booking_slot_status_trigger
  AFTER INSERT OR UPDATE OR DELETE ON bookings
  FOR EACH ROW 
  EXECUTE FUNCTION update_slot_booking_status();

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

CREATE TRIGGER time_slots_updated_at_trigger
  BEFORE UPDATE ON time_slots
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to bulk generate slots for multiple days
CREATE OR REPLACE FUNCTION generate_slots_bulk(
  p_pitch_id UUID,
  p_start_date DATE,
  p_end_date DATE,
  p_force_regenerate BOOLEAN DEFAULT FALSE
)
RETURNS TABLE(
  date_processed DATE,
  slots_created INTEGER,
  message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
  current_date DATE;
  result_record RECORD;
BEGIN
  -- Validate input dates
  IF p_start_date > p_end_date THEN
    RETURN QUERY SELECT NULL::DATE, 0, 'Error: Start date must be before end date';
    RETURN;
  END IF;
  
  IF p_end_date > CURRENT_DATE + INTERVAL '365 days' THEN
    RETURN QUERY SELECT NULL::DATE, 0, 'Error: Cannot generate slots more than 1 year in advance';
    RETURN;
  END IF;
  
  current_date := p_start_date;
  
  WHILE current_date <= p_end_date LOOP
    -- Generate slots for current date
    SELECT * INTO result_record 
    FROM generate_time_slots(p_pitch_id, current_date, p_force_regenerate);
    
    RETURN QUERY SELECT current_date, result_record.slots_created, result_record.message;
    
    current_date := current_date + INTERVAL '1 day';
  END LOOP;
END;
$$;

-- Function to clean up old slots (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_slots(
  p_days_to_keep INTEGER DEFAULT 30
)
RETURNS TABLE(
  deleted_count INTEGER,
  message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
  cutoff_date DATE;
  deleted_rows INTEGER;
BEGIN
  cutoff_date := CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep;
  
  DELETE FROM time_slots 
  WHERE slot_date < cutoff_date
    AND is_booked = FALSE; -- Only delete unbooked slots
    
  GET DIAGNOSTICS deleted_rows = ROW_COUNT;
  
  RETURN QUERY SELECT 
    deleted_rows,
    format('Deleted %s old unbooked slots before %s', deleted_rows, cutoff_date);
END;
$$;

-- Function to get slot statistics
CREATE OR REPLACE FUNCTION get_slot_stats(
  p_pitch_id UUID DEFAULT NULL,
  p_date_from DATE DEFAULT CURRENT_DATE,
  p_date_to DATE DEFAULT CURRENT_DATE + INTERVAL '7 days'
)
RETURNS TABLE(
  total_slots BIGINT,
  available_slots BIGINT,
  booked_slots BIGINT,
  utilization_percentage NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_slots,
    COUNT(*) FILTER (WHERE is_booked = FALSE AND is_available = TRUE) as available_slots,
    COUNT(*) FILTER (WHERE is_booked = TRUE) as booked_slots,
    ROUND(
      (COUNT(*) FILTER (WHERE is_booked = TRUE)::NUMERIC / COUNT(*)::NUMERIC) * 100, 
      2
    ) as utilization_percentage
  FROM time_slots
  WHERE (p_pitch_id IS NULL OR pitch_id = p_pitch_id)
    AND slot_date BETWEEN p_date_from AND p_date_to;
END;
$$;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE time_slots IS 'Stores all time slots for all pitches with pre-calculated booking status';
COMMENT ON COLUMN time_slots.pitch_id IS 'References the pitch this slot belongs to';
COMMENT ON COLUMN time_slots.slot_date IS 'The date of the slot (date only, no time)';
COMMENT ON COLUMN time_slots.start_time IS 'Start time of the slot (time only, no date)';
COMMENT ON COLUMN time_slots.end_time IS 'End time of the slot (time only, no date)';
COMMENT ON COLUMN time_slots.is_booked IS 'TRUE when slot is booked, automatically managed by triggers';
COMMENT ON COLUMN time_slots.is_available IS 'Manual override to disable specific slots';
COMMENT ON COLUMN time_slots.booking_id IS 'ID of the booking if slot is booked, NULL otherwise';
COMMENT ON COLUMN time_slots.price_per_hour IS 'Price for this slot, copied from pitch settings at creation';

COMMENT ON FUNCTION generate_time_slots IS 'Generates time slots for a specific pitch and date based on pitch settings';
COMMENT ON FUNCTION update_slot_booking_status IS 'Trigger function to automatically update slot booking status when bookings change';
COMMENT ON FUNCTION generate_slots_bulk IS 'Utility to generate slots for multiple days at once';
COMMENT ON FUNCTION cleanup_old_slots IS 'Maintenance function to remove old unbooked slots';
COMMENT ON FUNCTION get_slot_stats IS 'Analytics function to get slot utilization statistics';

-- =====================================================
-- INITIAL DATA VERIFICATION
-- =====================================================

-- Check that the schema was created correctly
DO $$
BEGIN
  -- Verify table exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables 
                 WHERE table_name = 'time_slots') THEN
    RAISE EXCEPTION 'time_slots table was not created properly';
  END IF;
  
  -- Verify constraints exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                 WHERE constraint_name = 'valid_time_range') THEN
    RAISE EXCEPTION 'valid_time_range constraint was not created';
  END IF;
  
  -- Verify functions exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.routines 
                 WHERE routine_name = 'generate_time_slots') THEN
    RAISE EXCEPTION 'generate_time_slots function was not created';
  END IF;
  
  -- Verify triggers exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.triggers 
                 WHERE trigger_name = 'booking_slot_status_trigger') THEN
    RAISE EXCEPTION 'booking_slot_status_trigger was not created';
  END IF;
  
  RAISE NOTICE 'Schema validation passed - all objects created successfully';
END;
$$;
