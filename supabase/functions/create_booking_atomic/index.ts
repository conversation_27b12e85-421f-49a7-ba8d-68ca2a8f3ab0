// Supabase Edge Function: create_booking_atomic
// Location: Supabase Dashboard > Edge Functions > New Function
// Name: create_booking_atomic

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role key for database operations
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Parse request body
    const { pitch_id, slot_start_time, slot_end_time, user_id } = await req.json()

    console.log('Atomic booking request:', {
      pitch_id,
      slot_start_time,
      slot_end_time,
      user_id
    })

    // Validate input parameters
    if (!pitch_id || !slot_start_time || !slot_end_time || !user_id) {
      return new Response(
        JSON.stringify({ 
          error: 'INVALID_INPUT', 
          message: 'Missing required fields: pitch_id, slot_start_time, slot_end_time, user_id' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Validate time range
    const startTime = new Date(slot_start_time)
    const endTime = new Date(slot_end_time)
    
    if (endTime <= startTime) {
      return new Response(
        JSON.stringify({ 
          error: 'INVALID_TIME_RANGE', 
          message: 'End time must be after start time' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if slot is in the past (with 5-minute grace period)
    const now = new Date()
    const gracePeriod = 5 * 60 * 1000 // 5 minutes in milliseconds
    
    if (startTime.getTime() < (now.getTime() - gracePeriod)) {
      return new Response(
        JSON.stringify({ 
          error: 'PAST_BOOKING', 
          message: 'Cannot book slots in the past' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get user's current active booking count (all pitches, only future bookings)
    const { data: userBookings, error: countError } = await supabaseClient
      .from('bookings')
      .select('id, slot_end_time')
      .eq('user_id', user_id)
      .in('status', ['confirmed', 'pending_payment'])
      .gt('slot_end_time', now.toISOString()) // Only count future/active bookings

    if (countError) {
      console.error('Error checking user booking count:', countError)
      return new Response(
        JSON.stringify({ 
          error: 'DATABASE_ERROR', 
          message: 'Failed to check user booking limit' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get pitch settings to check max_bookings_per_user
    const { data: pitchSettings, error: settingsError } = await supabaseClient
      .from('pitch_settings')
      .select('max_bookings_per_user')
      .eq('id', pitch_id)
      .single()

    if (settingsError || !pitchSettings) {
      console.error('Error fetching pitch settings:', settingsError)
      return new Response(
        JSON.stringify({ 
          error: 'PITCH_NOT_FOUND', 
          message: 'Invalid pitch ID' 
        }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if user has reached booking limit
    if (userBookings.length >= pitchSettings.max_bookings_per_user) {
      return new Response(
        JSON.stringify({ 
          error: 'BOOKING_LIMIT_EXCEEDED', 
          message: `You can only have ${pitchSettings.max_bookings_per_user} active bookings at a time` 
        }),
        { 
          status: 409, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Attempt to create the booking (this will trigger the unique constraint)
    const { data: newBooking, error: insertError } = await supabaseClient
      .from('bookings')
      .insert({
        user_id,
        pitch_id,
        slot_start_time,
        slot_end_time,
        status: 'confirmed',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (insertError) {
      console.error('Booking creation error:', insertError)
      
      // Handle unique constraint violation (double booking)
      if (insertError.code === '23505') {
        return new Response(
          JSON.stringify({ 
            error: 'SLOT_UNAVAILABLE', 
            message: 'This time slot was just booked by another user. Please select a different time.' 
          }),
          { 
            status: 409, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      // Handle other database errors
      return new Response(
        JSON.stringify({ 
          error: 'BOOKING_FAILED', 
          message: 'Failed to create booking due to database error' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Booking created successfully:', newBooking)

    // Return success response
    return new Response(
      JSON.stringify({ 
        success: true, 
        booking: newBooking,
        message: 'Booking created successfully' 
      }),
      { 
        status: 201, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Unexpected error in atomic booking function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'INTERNAL_ERROR', 
        message: 'An unexpected error occurred' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
