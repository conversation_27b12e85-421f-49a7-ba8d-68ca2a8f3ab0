-- Database Schema Verification Script
-- Run this in Supabase SQL Editor to check current schema status

-- Check if pitch_settings table has the required pricing fields
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'pitch_settings'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check current pitch_settings data
SELECT id, pitch_name, open_time, close_time, slot_duration_minutes, 
       cancellation_window_hours,
       -- These might not exist yet if migration not applied:
       CASE WHEN EXISTS (
         SELECT 1 FROM information_schema.columns 
         WHERE table_name = 'pitch_settings' 
         AND column_name = 'price_per_hour'
       ) THEN price_per_hour ELSE NULL END as price_per_hour,
       CASE WHEN EXISTS (
         SELECT 1 FROM information_schema.columns 
         WHERE table_name = 'pitch_settings' 
         AND column_name = 'max_bookings_per_user'  
       ) THEN max_bookings_per_user ELSE NULL END as max_bookings_per_user
FROM pitch_settings;

-- Check if bookings table has race condition prevention constraints
SELECT conname, contype, pg_get_constraintdef(c.oid) AS constraint_definition
FROM pg_constraint c
JOIN pg_namespace n ON n.oid = c.connamespace
WHERE conrelid = 'public.bookings'::regclass
AND contype IN ('u', 'c'); -- unique and check constraints

-- Check existing indexes on bookings table
SELECT indexname, indexdef
FROM pg_indexes
WHERE tablename = 'bookings'
AND schemaname = 'public';

-- Check if the atomic booking validation function exists
SELECT proname, prosrc
FROM pg_proc
WHERE proname = 'validate_booking_slot';
